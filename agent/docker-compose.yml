version: '3.8'

services:
  agent-memory:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8010:8010"
    env_file:
      - .env.docker
    volumes:
      - ./src:/app/src
    depends_on:
      ollama-setup:
        condition: service_completed_successfully
    networks:
      - agent-network
    restart: unless-stopped

  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ${HOME}/.ollama:/root/.ollama
    networks:
      - agent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    command: serve

  ollama-setup:
    image: curlimages/curl:latest
    depends_on:
      - ollama
    networks:
      - agent-network
    restart: "no"
    command: >
      sh -c "
        echo 'Waiting for Ollama to be ready...' &&
        until curl -f http://ollama:11434/api/tags; do
          echo 'Waiting for Ollama...'
          sleep 5
        done &&
        echo 'Pulling qwen2:1.5b model...' &&
        curl -X POST http://ollama:11434/api/pull -d '{\"name\": \"qwen2:1.5b\"}' &&
        echo 'Pulling snowflake-arctic-embed2 model...' &&
        curl -X POST http://ollama:11434/api/pull -d '{\"name\": \"snowflake-arctic-embed2\"}' &&
        echo 'All models pulled successfully!'
      "

networks:
  agent-network:
    driver: bridge
