"""
Agent Memory Service

This service provides memory management for the agent system using mem0.
It handles storing, retrieving, and analyzing user interactions.
"""

import os
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from uuid import UUID

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# Import mem0 components
from mem0 import Memory

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Agent Memory Service",
    description="Memory management for the agent system using mem0",
    version="0.1.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Update this in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize mem0 Memory
try:
    # Check if we should use local or remote configuration
    if os.getenv("MEM0_USE_LOCAL", "true").lower() == "true":
        # Local configuration with Supabase
        # Choose embedding model: nomic-embed-text (768 dims) or snowflake-arctic-embed2 (1024 dims)
        embedding_model = os.getenv("EMBEDDER_MODEL", "nomic-embed-text:latest")
        embedding_dims = 1024 if embedding_model == "snowflake-arctic-embed2" else 768

        # 修改connection_string来指定vecs schema
        connection_string = os.getenv("SUPABASE_DB_STRING", "postgresql://postgres:postgres@localhost:54322/postgres")
        # 添加search_path参数来指定schema
        if "?" in connection_string:
            connection_string += "&options=-c search_path=vecs,public"
        else:
            connection_string += "?options=-c search_path=vecs,public"

        # Configure LLM
        llm_provider = os.getenv("LLM_PROVIDER", "ollama")
        llm_config = {
            "model": os.getenv("LLM_MODEL", "qwen3:4b"),
            "temperature": 0,
            "max_tokens": 2000,
        }

        if llm_provider == "openai":
            llm_config.update({
                "api_key": os.getenv("OPENAI_API_KEY"),
            })
            # Note: OpenAI LLM doesn't support custom base_url in mem0
        else:
            llm_config["ollama_base_url"] = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")

        # Configure Embedder
        embedder_provider = os.getenv("EMBEDDER_PROVIDER", "ollama")
        embedder_config = {
            "model": embedding_model,
        }

        if embedder_provider == "openai":
            embedder_config.update({
                "api_key": os.getenv("OPENAI_API_KEY"),
            })
            # Note: OpenAI embedder doesn't support custom base_url in mem0
        else:
            embedder_config["ollama_base_url"] = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")

        config = {
            "vector_store": {
                "provider": "supabase",
                "config": {
                    "collection_name": "memories",
                    "connection_string": connection_string,
                    "index_method": "hnsw",
                    "index_measure": "cosine_distance",
                    "embedding_model_dims": embedding_dims,
                }
            },
            "llm": {
                "provider": llm_provider,
                "config": llm_config
            },
            "embedder": {
                "provider": embedder_provider,
                "config": embedder_config
            },
        }
        memory = Memory.from_config(config)
    else:
        # Use mem0 platform with API key
        memory = Memory()

    logger.info("Memory service initialized successfully with Supabase vector store")
except Exception as e:
    logger.error(f"Failed to initialize memory service: {str(e)}")
    memory = None


# Pydantic models
class Message(BaseModel):
    role: str
    content: str


class MemoryInput(BaseModel):
    messages: List[Message]
    user_id: str
    metadata: Optional[Dict[str, Any]] = None
    infer: bool = False  # Disable inference to prevent unwanted memory merging


class MemorySearchInput(BaseModel):
    query: str
    user_id: str
    limit: Optional[int] = 5


class MemoryUpdateInput(BaseModel):
    memory_id: str
    data: str


class MemoryResponse(BaseModel):
    id: str
    success: bool
    message: str


class MemorySearchResponse(BaseModel):
    memories: List[Dict[str, Any]]
    count: int


class IdeaResponse(BaseModel):
    id: str
    user_id: str
    title: str
    description: str
    type: str
    confidence: float
    created_at: str
    source: str


class IdeasResponse(BaseModel):
    ideas: List[IdeaResponse]
    count: int


# Dependency to check if memory service is available
async def get_memory():
    if memory is None:
        raise HTTPException(
            status_code=503,
            detail="Memory service is not available. Check server logs for details."
        )
    return memory


# In-memory storage for ideas (in production, use a proper database)
user_ideas_store = {}

# In-memory storage for inspirations (in production, use a proper database)
user_inspirations_store = {}

# Background task for memory analysis
async def analyze_memory(user_id: str):
    """
    Analyze user memories to generate insights.
    This runs as a background task.
    """
    try:
        logger.info(f"Starting memory analysis for user {user_id}")

        # Get memory client
        memory_client = await get_memory()
        if not memory_client:
            logger.error("Memory client not available for analysis")
            return

        # Import analyzer here to avoid circular imports
        from analyzer import MemoryAnalyzer

        # Create analyzer instance
        analyzer = MemoryAnalyzer(memory_client)

        # Perform analysis
        insights = await analyzer.analyze_user_memories(user_id)

        if insights:
            logger.info(f"Generated {len(insights)} insights for user {user_id}")
            # TODO: Store insights in database or cache for later retrieval
            # For now, just log the insights
            for insight in insights:
                logger.info(f"Insight for {user_id}: {insight['type']} - {insight['content']}")
        else:
            logger.info(f"No insights generated for user {user_id}")

        logger.info(f"Completed memory analysis for user {user_id}")
    except Exception as e:
        logger.error(f"Error in memory analysis for user {user_id}: {str(e)}")


# Helper function to enhance memory metadata with philosophical insights
async def enhance_memory_metadata(messages: List[Dict[str, Any]], existing_metadata: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract philosophical insights and enhance metadata for better idea discovery.

    Args:
        messages: List of message dictionaries
        existing_metadata: Existing metadata dictionary

    Returns:
        Enhanced metadata with philosophical insights
    """
    try:
        # Combine all message content
        content = " ".join([msg.get("content", "") for msg in messages if msg.get("content")])

        if not content.strip():
            return existing_metadata

        # Use external LLM to extract insights
        import aiohttp

        insight_prompt = f"""
Analyze the following text and extract:
1. Main topic/theme (1-2 words)
2. Any philosophical insight, wisdom, or deep thought
3. Emotional tone
4. Key concepts or ideas

Text: {content}

Respond in JSON format:
{{
    "topic": "main topic",
    "philosophical_insight": "deep thought or wisdom (empty if none)",
    "emotional_tone": "positive/negative/neutral/mixed",
    "key_concepts": ["concept1", "concept2"],
    "abstraction_level": "concrete/abstract/philosophical"
}}

Only return valid JSON, no additional text.
"""

        # Call external LLM
        api_base = os.getenv("EXTERNAL_LLM_BASE_URL", "http://*************:4033/")
        api_key = os.getenv("EXTERNAL_LLM_API_KEY", "sk-YyiBg6DSn1Fc2KBNU6ZYtw")
        model = os.getenv("EXTERNAL_LLM_MODEL", "qwen3-30b-a3b-mlx")

        if not api_base.endswith('/'):
            api_base += '/'

        url = f"{api_base}v1/chat/completions"

        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

        data = {
            "model": model,
            "messages": [
                {"role": "system", "content": "You are an expert at extracting philosophical insights and deep thoughts from text."},
                {"role": "user", "content": insight_prompt}
            ],
            "temperature": 0.3,
            "max_tokens": 500
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=data, timeout=15) as response:
                if response.status == 200:
                    result = await response.json()
                    llm_response = result["choices"][0]["message"]["content"]

                    try:
                        import json
                        insights = json.loads(llm_response.strip())

                        # Enhance existing metadata
                        enhanced_metadata = existing_metadata.copy()
                        enhanced_metadata.update({
                            "extracted_topic": insights.get("topic", ""),
                            "philosophical_insight": insights.get("philosophical_insight", ""),
                            "emotional_tone": insights.get("emotional_tone", "neutral"),
                            "key_concepts": insights.get("key_concepts", []),
                            "abstraction_level": insights.get("abstraction_level", "concrete"),
                            "insight_extraction_timestamp": datetime.now().isoformat()
                        })

                        logger.info(f"Enhanced metadata with insights: {insights.get('topic', 'unknown')} - {insights.get('philosophical_insight', 'none')[:50]}")
                        return enhanced_metadata

                    except json.JSONDecodeError:
                        logger.warning("Failed to parse insight extraction response")
                        return existing_metadata
                else:
                    logger.warning(f"Insight extraction API error: {response.status}")
                    return existing_metadata

    except Exception as e:
        logger.error(f"Error enhancing memory metadata: {e}")
        return existing_metadata


# Background task for idea discovery
async def discover_ideas(user_id: str):
    """
    Discover new ideas from user memories.
    This runs as a background task and may not always generate ideas.
    """
    try:
        logger.info(f"Starting idea discovery for user {user_id}")

        # Get memory client
        memory_client = await get_memory()
        if not memory_client:
            logger.error("Memory client not available for idea discovery")
            return

        # Import analyzer here to avoid circular imports
        from analyzer import MemoryAnalyzer

        # Create analyzer instance
        analyzer = MemoryAnalyzer(memory_client)

        # Perform idea discovery
        ideas = await analyzer.discover_ideas(user_id)

        if ideas:
            logger.info(f"Discovered {len(ideas)} new ideas for user {user_id}")

            # Store ideas in memory (in production, use database)
            if user_id not in user_ideas_store:
                user_ideas_store[user_id] = []

            user_ideas_store[user_id].extend(ideas)

            # Keep only the latest 50 ideas per user
            user_ideas_store[user_id] = user_ideas_store[user_id][-50:]

            # Log the discovered ideas
            for idea in ideas:
                logger.info(f"New idea for {user_id}: {idea['title']} - {idea['description']}")
        else:
            logger.info(f"No new ideas discovered for user {user_id}")

        logger.info(f"Completed idea discovery for user {user_id}")
    except Exception as e:
        logger.error(f"Error in idea discovery for user {user_id}: {str(e)}")


# Background task for inspiration mining
async def mine_inspiration(user_id: str, current_memory_content: str):
    """
    Mine inspiration from user's current operation and memory context.
    This runs as a background task to provide non-intrusive insights.
    """
    try:
        logger.info(f"Starting inspiration mining for user {user_id}")

        # Get memory client
        memory_client = await get_memory()
        if not memory_client:
            logger.error("Memory client not available for inspiration mining")
            return

        # Get recent memories for context
        try:
            recent_memories = memory_client.get_all(user_id=user_id)
            if isinstance(recent_memories, list):
                memories_list = recent_memories[-10:]  # Get last 10 memories
            else:
                memories_list = []
        except Exception as e:
            logger.warning(f"Could not retrieve recent memories: {e}")
            memories_list = []

        # Extract content from memories
        memory_contexts = []
        for memory in memories_list:
            if isinstance(memory, dict):
                content = memory.get("memory", memory.get("content", ""))
                if content:
                    memory_contexts.append(content)

        # Use external LLM for deep insight mining
        inspiration = await generate_inspiration_with_llm(
            user_id, current_memory_content, memory_contexts
        )

        if inspiration:
            logger.info(f"Generated inspiration for user {user_id}: {inspiration['title']}")

            # Store inspiration in user store
            if user_id not in user_inspirations_store:
                user_inspirations_store[user_id] = []

            user_inspirations_store[user_id].append(inspiration)

            # Keep only the latest 20 inspirations per user
            user_inspirations_store[user_id] = user_inspirations_store[user_id][-20:]
        else:
            logger.info(f"No inspiration generated for user {user_id}")

        logger.info(f"Completed inspiration mining for user {user_id}")
    except Exception as e:
        logger.error(f"Error in inspiration mining for user {user_id}: {str(e)}")


# Helper function to generate inspiration using external LLM
async def generate_inspiration_with_llm(user_id: str, current_content: str, memory_contexts: List[str]) -> Optional[Dict[str, Any]]:
    """
    Generate inspiration using external LLM with creative prompts.

    Args:
        user_id: User identifier
        current_content: Current memory content being saved
        memory_contexts: List of recent memory contents for context

    Returns:
        Inspiration dictionary or None if no inspiration generated
    """
    try:
        # Build context from recent memories
        context_text = ""
        if memory_contexts:
            context_text = "\n".join([f"- {ctx[:100]}..." for ctx in memory_contexts[-5:]])

        # Creative prompt for inspiration mining
        inspiration_prompt = f"""
You are a wise, observant AI companion watching over a user's intellectual journey.
The user just saved this new memory: "{current_content}"

Their recent thoughts and memories include:
{context_text}

As their thoughtful observer, generate a profound, non-intrusive insight that could inspire them. Look for:
1. Hidden connections between their current thought and past memories
2. Philosophical implications or deeper meanings
3. Creative possibilities they might not have considered
4. Patterns in their thinking that could lead to breakthroughs

Respond in JSON format:
{{
    "title": "Brief inspiring title (max 50 chars)",
    "insight": "The main inspirational insight (max 200 chars)",
    "type": "connection|pattern|possibility|wisdom",
    "confidence": 0.1-1.0,
    "reasoning": "Why this insight emerged from their memories (max 150 chars)"
}}

Only generate an insight if you find a genuinely meaningful connection. Return empty JSON {{}} if no strong insight emerges.
"""

        # Call external LLM
        api_base = os.getenv("EXTERNAL_LLM_BASE_URL", "http://*************:4033/")
        api_key = os.getenv("EXTERNAL_LLM_API_KEY", "sk-YyiBg6DSn1Fc2KBNU6ZYtw")
        model = os.getenv("EXTERNAL_LLM_MODEL", "qwen3-30b-a3b-mlx")

        if not api_base.endswith('/'):
            api_base += '/'

        url = f"{api_base}v1/chat/completions"

        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

        data = {
            "model": model,
            "messages": [
                {"role": "system", "content": "You are a wise AI companion that provides profound, non-intrusive insights by observing patterns in human thought."},
                {"role": "user", "content": inspiration_prompt}
            ],
            "temperature": 0.7,  # Higher temperature for creativity
            "max_tokens": 400
        }

        import aiohttp
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=data, timeout=20) as response:
                if response.status == 200:
                    result = await response.json()
                    llm_response = result["choices"][0]["message"]["content"]

                    try:
                        import json
                        inspiration_data = json.loads(llm_response.strip())

                        # Check if we got a valid inspiration
                        if inspiration_data and inspiration_data.get("title") and inspiration_data.get("insight"):
                            # Add metadata
                            inspiration_data.update({
                                "id": f"insp_{user_id}_{int(datetime.now().timestamp())}",
                                "user_id": user_id,
                                "created_at": datetime.now().isoformat(),
                                "source": "memory_submission",
                                "trigger_content": current_content[:100]
                            })

                            logger.info(f"Generated inspiration: {inspiration_data['title']}")
                            return inspiration_data
                        else:
                            logger.info("LLM decided no strong insight was available")
                            return None

                    except json.JSONDecodeError:
                        logger.warning("Failed to parse inspiration response")
                        return None
                else:
                    logger.warning(f"Inspiration API error: {response.status}")
                    return None

    except Exception as e:
        logger.error(f"Error generating inspiration: {e}")
        return None


# API endpoints
@app.get("/")
async def root():
    """Health check endpoint with model information"""
    # Get model configuration from environment variables
    llm_provider = os.getenv("LLM_PROVIDER", "ollama")
    llm_model = os.getenv("LLM_MODEL", "qwen3:4b")
    embedder_provider = os.getenv("EMBEDDER_PROVIDER", "ollama")
    embedder_model = os.getenv("EMBEDDER_MODEL", "nomic-embed-text:latest")

    # Determine embedding dimensions
    embedding_dims = 1024 if embedder_model == "snowflake-arctic-embed2" else 768

    # Get base URLs based on provider
    llm_base_url = os.getenv("OPENAI_BASE_URL") if llm_provider == "openai" else os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
    embedder_base_url = os.getenv("OPENAI_BASE_URL") if embedder_provider == "openai" else os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")

    return {
        "status": "ok",
        "service": "Agent Memory",
        "version": "0.1.0",
        "models": {
            "llm": {
                "provider": llm_provider,
                "model": llm_model,
                "base_url": llm_base_url
            },
            "embedder": {
                "provider": embedder_provider,
                "model": embedder_model,
                "dimensions": embedding_dims,
                "base_url": embedder_base_url
            }
        },
        "vector_store": {
            "provider": "supabase",
            "collection": "memories",
            "index_method": "hnsw",
            "distance_metric": "cosine_distance"
        },
        "memory_service_available": memory is not None
    }


@app.get("/models")
async def get_models():
    """Get detailed information about the models being used"""
    llm_provider = os.getenv("LLM_PROVIDER", "ollama")
    llm_model = os.getenv("LLM_MODEL", "qwen3:4b")
    embedder_provider = os.getenv("EMBEDDER_PROVIDER", "ollama")
    embedder_model = os.getenv("EMBEDDER_MODEL", "nomic-embed-text:latest")

    # Determine embedding dimensions
    embedding_dims = 1024 if embedder_model == "snowflake-arctic-embed2" else 768

    # Get base URLs based on provider
    llm_base_url = os.getenv("OPENAI_BASE_URL") if llm_provider == "openai" else os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
    embedder_base_url = os.getenv("OPENAI_BASE_URL") if embedder_provider == "openai" else os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")

    return {
        "inference_model": {
            "provider": llm_provider,
            "model": llm_model,
            "base_url": llm_base_url,
            "temperature": 0,
            "max_tokens": 2000,
            "description": "Large Language Model used for reasoning and inference"
        },
        "embedding_model": {
            "provider": embedder_provider,
            "model": embedder_model,
            "dimensions": embedding_dims,
            "base_url": embedder_base_url,
            "description": "Embedding model used for vector representations"
        },
        "vector_store": {
            "provider": "supabase",
            "collection": "memories",
            "index_method": "hnsw",
            "distance_metric": "cosine_distance",
            "description": "Vector database for storing and searching embeddings"
        },
        "configuration": {
            "mem0_use_local": os.getenv("MEM0_USE_LOCAL", "true"),
            "analysis_enabled": os.getenv("ANALYSIS_ENABLED", "true"),
            "analysis_interval_minutes": os.getenv("ANALYSIS_INTERVAL_MINUTES", "60")
        }
    }


@app.post("/memories", response_model=MemoryResponse)
async def add_memory(
    memory_input: MemoryInput,
    background_tasks: BackgroundTasks,
    mem: Memory = Depends(get_memory)
):
    """
    Add a new memory from user interaction with enhanced metadata extraction
    """
    try:
        # Convert Pydantic messages to dict format expected by mem0
        messages = [msg.model_dump() for msg in memory_input.messages]

        # Extract philosophical insights and enhance metadata
        enhanced_metadata = await enhance_memory_metadata(messages, memory_input.metadata or {})

        # Add memory using mem0 with enhanced metadata
        result = mem.add(
            messages,
            user_id=memory_input.user_id,
            metadata=enhanced_metadata,
            infer=memory_input.infer
        )

        # Schedule background analysis
        background_tasks.add_task(analyze_memory, memory_input.user_id)

        # Schedule background idea discovery (with some randomness to avoid overwhelming)
        import random
        if random.random() < 0.4:  # 40% chance to trigger idea discovery
            background_tasks.add_task(discover_ideas, memory_input.user_id)

        # Always trigger inspiration mining for current memory content
        current_content = " ".join([msg.content for msg in memory_input.messages])
        background_tasks.add_task(mine_inspiration, memory_input.user_id, current_content)

        return {
            "id": result.get("id", "unknown"),
            "success": True,
            "message": "Memory added successfully"
        }
    except Exception as e:
        logger.error(f"Error adding memory: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to add memory: {str(e)}")


@app.get("/memories/{user_id}", response_model=MemorySearchResponse)
async def get_all_memories(
    user_id: str,
    mem: Memory = Depends(get_memory)
):
    """
    Get all memories for a user - using direct database query for speed
    """
    try:
        logger.info(f"Getting all memories for user: {user_id}")

        # Use direct database query for better performance
        # Skip mem0's get_all() method due to known bugs and performance issues
        try:
            import psycopg2

            connection_string = os.getenv("SUPABASE_DB_STRING", "postgresql://postgres:postgres@localhost:54322/postgres")
            conn = psycopg2.connect(connection_string)
            cur = conn.cursor()

            # Query vecs.memories table directly
            # The memory content is in metadata->>'data'
            # The timestamps are in metadata->>'created_at' and metadata->>'updated_at'
            cur.execute("""
                SELECT id,
                       metadata->>'data' as memory,
                       metadata,
                       COALESCE(metadata->>'created_at', NOW()::text) as created_at,
                       COALESCE(metadata->>'updated_at', metadata->>'created_at', NOW()::text) as updated_at
                FROM vecs.memories
                WHERE metadata->>'user_id' = %s
                ORDER BY COALESCE(metadata->>'created_at', NOW()::text) DESC
            """, (user_id,))

            rows = cur.fetchall()
            logger.info(f"Direct DB query found {len(rows)} memories for user {user_id}")

            memories_list = []
            for row in rows:
                memory_dict = {
                    "id": row[0],
                    "memory": row[1],
                    "metadata": row[2] if row[2] else {},
                    "created_at": row[3] if row[3] else None,
                    "updated_at": row[4] if row[4] else None,
                }
                memories_list.append(memory_dict)

            conn.close()

            return {
                "memories": memories_list,
                "count": len(memories_list)
            }

        except Exception as db_error:
            logger.error(f"Direct database query failed: {db_error}")
            logger.info("Falling back to mem0 get_all() method...")

            # Fallback to mem0 method if direct query fails
            try:
                memories = mem.get_all(user_id=user_id)
                logger.info(f"mem0 memories response type: {type(memories)}")

                # Process mem0 response
                if isinstance(memories, list):
                    memories_list = memories
                elif isinstance(memories, dict):
                    if 'results' in memories:
                        memories_list = memories['results']
                    elif 'memories' in memories:
                        memories_list = memories['memories']
                    else:
                        memories_list = [memories] if memories else []
                else:
                    logger.warning(f"Unexpected memories type: {type(memories)}")
                    memories_list = []

                return {
                    "memories": memories_list,
                    "count": len(memories_list)
                }

            except AttributeError as attr_error:
                if "'list' object has no attribute 'id'" in str(attr_error):
                    logger.warning(f"Known mem0 bug encountered: {attr_error}")
                    return {
                        "memories": [],
                        "count": 0
                    }
                else:
                    raise attr_error

    except Exception as e:
        logger.error(f"Error retrieving memories: {str(e)}")
        logger.error(f"Exception type: {type(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve memories: {str(e)}")


@app.post("/memories/search", response_model=MemorySearchResponse)
async def search_memories(
    search_input: MemorySearchInput,
    mem: Memory = Depends(get_memory)
):
    """
    Search for relevant memories
    """
    try:
        logger.info(f"Searching memories for user: {search_input.user_id}, query: {search_input.query}")
        memories = mem.search(
            query=search_input.query,
            user_id=search_input.user_id,
            limit=search_input.limit
        )
        logger.info(f"Search memories response type: {type(memories)}")
        logger.info(f"Search memories response: {memories}")

        # Handle the case where memories might be a list or dict with results
        if isinstance(memories, list):
            memories_list = memories
        elif isinstance(memories, dict):
            # Handle case where it might return a dict with results
            if 'results' in memories:
                memories_list = memories['results']
            elif 'memories' in memories:
                memories_list = memories['memories']
            else:
                # If it's a single memory dict, wrap it in a list
                memories_list = [memories] if memories else []
        else:
            logger.warning(f"Unexpected search memories type: {type(memories)}, value: {memories}")
            memories_list = []

        logger.info(f"Processed search memories count: {len(memories_list)}")

        return {
            "memories": memories_list,
            "count": len(memories_list)
        }
    except Exception as e:
        logger.error(f"Error searching memories: {str(e)}")
        logger.error(f"Exception type: {type(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Failed to search memories: {str(e)}")


@app.put("/memories", response_model=MemoryResponse)
async def update_memory(
    update_input: MemoryUpdateInput,
    mem: Memory = Depends(get_memory)
):
    """
    Update an existing memory
    """
    try:
        result = mem.update(
            memory_id=update_input.memory_id,
            data=update_input.data
        )
        # Handle different return types from mem0
        memory_id = update_input.memory_id
        if isinstance(result, dict) and 'id' in result:
            memory_id = result['id']
        elif isinstance(result, list) and len(result) > 0 and isinstance(result[0], dict) and 'id' in result[0]:
            memory_id = result[0]['id']

        return {
            "id": memory_id,
            "success": True,
            "message": "Memory updated successfully"
        }
    except Exception as e:
        logger.error(f"Error updating memory: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update memory: {str(e)}")


@app.delete("/memories/{memory_id}", response_model=MemoryResponse)
async def delete_memory(
    memory_id: str,
    mem: Memory = Depends(get_memory)
):
    """
    Delete a specific memory
    """
    try:
        result = mem.delete(memory_id=memory_id)
        # Handle different return types from mem0
        return {
            "id": memory_id,
            "success": True,
            "message": "Memory deleted successfully"
        }
    except Exception as e:
        logger.error(f"Error deleting memory: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete memory: {str(e)}")


@app.delete("/memories/user/{user_id}", response_model=MemoryResponse)
async def delete_all_user_memories(
    user_id: str,
    mem: Memory = Depends(get_memory)
):
    """
    Delete all memories for a user
    """
    try:
        mem.delete_all(user_id=user_id)
        return {
            "id": user_id,
            "success": True,
            "message": f"All memories for user {user_id} deleted successfully"
        }
    except Exception as e:
        logger.error(f"Error deleting user memories: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete user memories: {str(e)}")


@app.get("/ideas/{user_id}", response_model=IdeasResponse)
async def get_user_ideas(user_id: str):
    """
    Get all discovered ideas for a user
    """
    try:
        ideas = user_ideas_store.get(user_id, [])

        # Convert to response format
        idea_responses = []
        for idea in ideas:
            idea_responses.append(IdeaResponse(**idea))

        return {
            "ideas": idea_responses,
            "count": len(idea_responses)
        }
    except Exception as e:
        logger.error(f"Error getting user ideas: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get user ideas: {str(e)}")


@app.post("/ideas/discover/{user_id}", response_model=MemoryResponse)
async def trigger_idea_discovery(
    user_id: str,
    background_tasks: BackgroundTasks
):
    """
    Manually trigger idea discovery for a user
    """
    try:
        # Schedule background idea discovery
        background_tasks.add_task(discover_ideas, user_id)

        return {
            "id": user_id,
            "success": True,
            "message": f"Idea discovery triggered for user {user_id}"
        }
    except Exception as e:
        logger.error(f"Error triggering idea discovery: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to trigger idea discovery: {str(e)}")


@app.get("/ideas/{user_id}/new")
async def check_new_ideas(user_id: str, last_check: Optional[str] = None):
    """
    Check if there are new ideas since last check
    Returns count of new ideas for frontend polling
    """
    try:
        ideas = user_ideas_store.get(user_id, [])

        if not last_check:
            return {"new_ideas_count": len(ideas), "has_new_ideas": len(ideas) > 0}

        # Parse last_check timestamp
        from datetime import datetime
        try:
            last_check_dt = datetime.fromisoformat(last_check.replace('Z', '+00:00'))
            new_ideas = [
                idea for idea in ideas
                if datetime.fromisoformat(idea['created_at'].replace('Z', '+00:00')) > last_check_dt
            ]
            return {"new_ideas_count": len(new_ideas), "has_new_ideas": len(new_ideas) > 0}
        except ValueError:
            # Invalid timestamp format, return all ideas
            return {"new_ideas_count": len(ideas), "has_new_ideas": len(ideas) > 0}

    except Exception as e:
        logger.error(f"Error checking new ideas: {str(e)}")
        return {"new_ideas_count": 0, "has_new_ideas": False}


# Inspiration API endpoints
@app.get("/inspirations/{user_id}")
async def get_user_inspirations(user_id: str):
    """
    Get all inspirations for a user
    """
    try:
        inspirations = user_inspirations_store.get(user_id, [])

        return {
            "inspirations": inspirations,
            "count": len(inspirations)
        }
    except Exception as e:
        logger.error(f"Error getting user inspirations: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get user inspirations: {str(e)}")


@app.get("/inspirations/{user_id}/new")
async def check_new_inspirations(user_id: str, last_check: Optional[str] = None):
    """
    Check if there are new inspirations since last check
    Returns count of new inspirations for frontend polling
    """
    try:
        inspirations = user_inspirations_store.get(user_id, [])

        if not last_check:
            return {"new_inspirations_count": len(inspirations), "has_new_inspirations": len(inspirations) > 0}

        # Parse last_check timestamp
        from datetime import datetime
        try:
            last_check_dt = datetime.fromisoformat(last_check.replace('Z', '+00:00'))
            new_inspirations = [
                inspiration for inspiration in inspirations
                if datetime.fromisoformat(inspiration['created_at'].replace('Z', '+00:00')) > last_check_dt
            ]
            return {"new_inspirations_count": len(new_inspirations), "has_new_inspirations": len(new_inspirations) > 0}
        except ValueError:
            # Invalid timestamp format, return all inspirations
            return {"new_inspirations_count": len(inspirations), "has_new_inspirations": len(inspirations) > 0}

    except Exception as e:
        logger.error(f"Error checking new inspirations: {str(e)}")
        return {"new_inspirations_count": 0, "has_new_inspirations": False}


@app.post("/inspirations/trigger/{user_id}")
async def trigger_inspiration_mining(
    user_id: str,
    background_tasks: BackgroundTasks,
    content: Optional[str] = None
):
    """
    Manually trigger inspiration mining for a user
    """
    try:
        # Use provided content or get latest memory content
        if not content:
            # Get latest memory content as context
            try:
                memory_client = await get_memory()
                recent_memories = memory_client.get_all(user_id=user_id)
                if isinstance(recent_memories, list) and recent_memories:
                    latest_memory = recent_memories[-1]
                    content = latest_memory.get("memory", latest_memory.get("content", ""))
                else:
                    content = "General inspiration request"
            except Exception as e:
                logger.warning(f"Could not get latest memory for inspiration: {e}")
                content = "General inspiration request"

        # Schedule background inspiration mining
        background_tasks.add_task(mine_inspiration, user_id, content)

        return {
            "id": user_id,
            "success": True,
            "message": f"Inspiration mining triggered for user {user_id}"
        }
    except Exception as e:
        logger.error(f"Error triggering inspiration mining: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to trigger inspiration mining: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8011, reload=True)
