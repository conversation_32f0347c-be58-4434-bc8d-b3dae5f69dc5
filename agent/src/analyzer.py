"""
Memory Analyzer Module

This module provides functionality to analyze user memories and generate insights and ideas.
"""

import logging
import os
import json
import random
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


class MemoryAnalyzer:
    """
    Analyzes user memories to extract patterns and generate insights.
    """

    def __init__(self, memory_client):
        """
        Initialize the memory analyzer with a memory client.

        Args:
            memory_client: An instance of mem0 Memory client
        """
        self.memory = memory_client

    async def analyze_user_memories(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Analyze memories for a specific user and generate insights.

        Args:
            user_id: The ID of the user whose memories to analyze

        Returns:
            A list of insights generated from the user's memories
        """
        try:
            # Retrieve all memories for the user
            memories_result = self.memory.get_all(user_id=user_id)

            # Handle different return formats from mem0
            memories = self._extract_memories_list(memories_result)

            if not memories:
                logger.info(f"No memories found for user {user_id}")
                return []

            logger.info(f"Analyzing {len(memories)} memories for user {user_id}")

            # Group memories by type/category
            categorized_memories = self._categorize_memories(memories)

            # Generate insights based on categorized memories
            insights = []

            # Process preferences
            if "preferences" in categorized_memories:
                preference_insights = self._analyze_preferences(categorized_memories["preferences"])
                insights.extend(preference_insights)

            # Process interests
            if "interests" in categorized_memories:
                interest_insights = self._analyze_interests(categorized_memories["interests"])
                insights.extend(interest_insights)

            # Process temporal patterns
            temporal_insights = self._analyze_temporal_patterns(memories)
            insights.extend(temporal_insights)

            # Process connections between topics
            connection_insights = self._analyze_connections(memories)
            insights.extend(connection_insights)

            logger.info(f"Generated {len(insights)} insights for user {user_id}")
            return insights

        except Exception as e:
            logger.error(f"Error analyzing memories for user {user_id}: {str(e)}")
            return []

    def _extract_memories_list(self, memories_result: Any) -> List[Dict[str, Any]]:
        """
        Extract memories list from different return formats of mem0.

        Args:
            memories_result: The result from mem0 get_all

        Returns:
            A list of memory dictionaries
        """
        if isinstance(memories_result, dict) and 'results' in memories_result:
            return memories_result['results']
        elif isinstance(memories_result, list):
            return memories_result
        else:
            return []

    def _categorize_memories(self, memories: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Categorize memories based on their content and metadata.

        Args:
            memories: List of memory objects

        Returns:
            Dictionary mapping categories to lists of memories
        """
        categories = {}

        for memory in memories:
            # Handle different memory formats
            if isinstance(memory, dict):
                # Extract category from metadata if available
                metadata = memory.get("metadata", {})
                if isinstance(metadata, dict):
                    category = metadata.get("category", "uncategorized")
                else:
                    category = "uncategorized"
            else:
                category = "uncategorized"

            if category not in categories:
                categories[category] = []

            categories[category].append(memory)

        return categories

    def _analyze_preferences(self, preference_memories: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Analyze preference-related memories to extract user preferences.

        Args:
            preference_memories: List of preference-related memories

        Returns:
            List of preference insights
        """
        insights = []

        if preference_memories:
            # Extract memory IDs safely
            memory_ids = []
            for memory in preference_memories[:3]:
                if isinstance(memory, dict) and "id" in memory:
                    memory_ids.append(memory["id"])

            insights.append({
                "type": "preference",
                "content": "User has expressed preferences that could be relevant to their current task",
                "confidence": 0.8,
                "related_memories": memory_ids,
                "created_at": datetime.now().isoformat()
            })

        return insights

    def _analyze_interests(self, interest_memories: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Analyze interest-related memories to identify user interests.

        Args:
            interest_memories: List of interest-related memories

        Returns:
            List of interest insights
        """
        insights = []

        if interest_memories:
            # Extract memory IDs safely
            memory_ids = []
            for memory in interest_memories[:3]:
                if isinstance(memory, dict) and "id" in memory:
                    memory_ids.append(memory["id"])

            insights.append({
                "type": "interest",
                "content": "Based on past interactions, the user might be interested in exploring related topics",
                "confidence": 0.7,
                "related_memories": memory_ids,
                "created_at": datetime.now().isoformat()
            })

        return insights

    def _analyze_temporal_patterns(self, memories: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Analyze temporal patterns in user memories.

        Args:
            memories: List of all user memories

        Returns:
            List of temporal pattern insights
        """
        insights = []

        # Example: Check if user has been consistently active
        if len(memories) > 5:
            # Extract memory IDs safely
            memory_ids = []
            for memory in memories[-5:]:
                if isinstance(memory, dict) and "id" in memory:
                    memory_ids.append(memory["id"])

            insights.append({
                "type": "activity",
                "content": "User has been consistently engaging with the system",
                "confidence": 0.6,
                "related_memories": memory_ids,
                "created_at": datetime.now().isoformat()
            })

        return insights

    def _analyze_connections(self, memories: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Analyze connections between different topics in user memories.

        Args:
            memories: List of all user memories

        Returns:
            List of connection insights
        """
        insights = []

        if len(memories) > 3:
            # Extract memory IDs safely
            memory_ids = []
            for memory in memories[-3:]:
                if isinstance(memory, dict) and "id" in memory:
                    memory_ids.append(memory["id"])

            insights.append({
                "type": "connection",
                "content": "There might be connections between the user's recent topics of interest",
                "confidence": 0.5,
                "related_memories": memory_ids,
                "created_at": datetime.now().isoformat()
            })

        return insights

    async def discover_ideas(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Discover new ideas and inspirations from user memories using creative prompts.

        This method implements advanced idea discovery by:
        1. Detecting topic switches in recent memories
        2. Finding distant/diverse memories for cross-pollination
        3. Extracting philosophical insights from memories
        4. Generating ideas through cross-topic fusion

        Args:
            user_id: The user ID to analyze memories for

        Returns:
            A list of discovered ideas/inspirations
        """
        try:
            # Retrieve all memories for the user
            memories_result = self.memory.get_all(user_id=user_id)
            memories = self._extract_memories_list(memories_result)

            if not memories or len(memories) < 5:
                logger.info(f"Not enough memories ({len(memories) if memories else 0}) for idea discovery for user {user_id}")
                return []

            logger.info(f"Discovering ideas from {len(memories)} memories for user {user_id}")

            # Step 1: Detect if there's a topic switch in recent memories
            topic_switch_detected = await self._detect_topic_switch(memories)

            if not topic_switch_detected:
                logger.info(f"No topic switch detected for user {user_id}, skipping idea discovery")
                return []

            # Step 2: Extract philosophical insights and categorize memories by topics
            enriched_memories = await self._enrich_memories_with_insights(memories)

            # Step 3: Find diverse memory combinations for idea generation
            memory_combinations = self._find_diverse_memory_combinations(enriched_memories)

            if not memory_combinations:
                logger.info(f"No diverse memory combinations found for user {user_id}")
                return []

            # Step 4: Generate ideas through cross-topic fusion
            ideas = await self._generate_fusion_ideas(memory_combinations, user_id)

            logger.info(f"Generated {len(ideas)} ideas for user {user_id}")
            return ideas

        except Exception as e:
            logger.error(f"Error discovering ideas for user {user_id}: {str(e)}")
            return []

    async def _generate_creative_ideas(self, memory_contents: List[str], user_id: str) -> List[Dict[str, Any]]:
        """
        Generate creative ideas using LLM with innovative prompts.

        Args:
            memory_contents: List of memory content strings
            user_id: The user ID

        Returns:
            List of generated ideas
        """
        try:
            # Combine recent memories (limit to avoid token overflow)
            recent_memories = memory_contents[-10:]  # Last 10 memories
            combined_content = "\n".join(recent_memories)

            # Creative idea discovery prompt
            creative_prompt = f"""
Based on the following user memories and experiences, discover 1-3 innovative ideas, insights, or creative inspirations. Think outside the box and make unexpected connections.

User Memories:
{combined_content}

Please generate creative ideas that:
1. Connect seemingly unrelated concepts from the memories
2. Suggest new perspectives or approaches
3. Identify potential opportunities or innovations
4. Propose creative solutions or improvements
5. Inspire new directions for exploration

For each idea, provide:
- A catchy title (max 50 characters)
- A brief description (max 200 characters)
- The type of idea (innovation, connection, opportunity, solution, inspiration)
- Confidence level (0.1-1.0)

Respond in JSON format as an array of objects with keys: title, description, type, confidence.
Only return valid JSON, no additional text.
"""

            # Use the memory client's LLM to generate ideas
            try:
                # Try to access the LLM through the memory client
                if hasattr(self.memory, 'llm') and hasattr(self.memory.llm, 'generate_response'):
                    response = await self.memory.llm.generate_response(creative_prompt)
                else:
                    # Fallback: use external LLM service
                    response = await self._call_external_llm(creative_prompt)

                # Parse the response
                ideas_data = json.loads(response.strip())

                # Convert to our format
                ideas = []
                for idea_data in ideas_data:
                    if isinstance(idea_data, dict):
                        ideas.append({
                            "id": f"idea_{user_id}_{datetime.now().timestamp()}_{random.randint(1000, 9999)}",
                            "user_id": user_id,
                            "title": idea_data.get("title", "Untitled Idea")[:50],
                            "description": idea_data.get("description", "No description")[:200],
                            "type": idea_data.get("type", "inspiration"),
                            "confidence": float(idea_data.get("confidence", 0.5)),
                            "created_at": datetime.now().isoformat(),
                            "source": "memory_analysis"
                        })

                return ideas

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse LLM response as JSON: {e}")
                return []
            except Exception as e:
                logger.error(f"Error calling LLM for idea generation: {e}")
                return []

        except Exception as e:
            logger.error(f"Error in creative idea generation: {e}")
            return []

    async def _call_external_llm(self, prompt: str) -> str:
        """
        Call external LLM service for idea generation.

        Args:
            prompt: The prompt to send to the LLM

        Returns:
            The LLM response
        """
        try:
            import aiohttp

            # Use external OpenAI-compatible API
            api_base = os.getenv("EXTERNAL_LLM_BASE_URL", "http://47.117.124.84:4033/")
            api_key = os.getenv("EXTERNAL_LLM_API_KEY", "sk-YyiBg6DSn1Fc2KBNU6ZYtw")
            model = os.getenv("EXTERNAL_LLM_MODEL", "qwen3-30b-a3b-mlx")

            if not api_base.endswith('/'):
                api_base += '/'

            url = f"{api_base}v1/chat/completions"

            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }

            data = {
                "model": model,
                "messages": [
                    {"role": "system", "content": "You are a creative AI assistant that generates innovative ideas and insights."},
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.8,  # Higher temperature for creativity
                "max_tokens": 1000
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=data, timeout=30) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result["choices"][0]["message"]["content"]
                    else:
                        logger.error(f"External LLM API error: {response.status}")
                        return "[]"

        except Exception as e:
            logger.error(f"Error calling external LLM: {e}")
            return "[]"

    async def _detect_topic_switch(self, memories: List[Dict[str, Any]]) -> bool:
        """
        Detect if there's a topic switch in recent memories.

        Args:
            memories: List of all user memories

        Returns:
            True if a topic switch is detected, False otherwise
        """
        try:
            if len(memories) < 3:
                return False

            # Get the last 3 memories for topic analysis
            recent_memories = memories[-3:]
            memory_contents = []

            for memory in recent_memories:
                if isinstance(memory, dict) and "memory" in memory:
                    memory_contents.append(memory["memory"])

            if len(memory_contents) < 3:
                return False

            # Use LLM to analyze topic diversity
            topic_analysis_prompt = f"""
Analyze the following three recent memories and determine if there's a significant topic switch or diversity:

Memory 1: {memory_contents[0]}
Memory 2: {memory_contents[1]}
Memory 3: {memory_contents[2]}

Determine if these memories represent:
1. The same topic/theme (low diversity)
2. Related but different aspects (medium diversity)
3. Completely different topics (high diversity)

Respond with only one word: "low", "medium", or "high"
"""

            response = await self._call_external_llm(topic_analysis_prompt)
            diversity_level = response.strip().lower()

            # Topic switch detected if diversity is medium or high
            topic_switch = diversity_level in ["medium", "high"]

            logger.info(f"Topic diversity analysis: {diversity_level}, switch detected: {topic_switch}")
            return topic_switch

        except Exception as e:
            logger.error(f"Error detecting topic switch: {e}")
            # Default to True to allow idea discovery
            return True



    async def _enrich_memories_with_insights(self, memories: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Enrich memories with philosophical insights and topic categorization.

        Args:
            memories: List of raw memories

        Returns:
            List of enriched memories with insights and topics
        """
        enriched_memories = []

        try:
            for memory in memories:
                if not isinstance(memory, dict) or "memory" not in memory:
                    continue

                content = memory["memory"]

                # Extract philosophical insights and topic
                insight_prompt = f"""
Analyze the following memory and extract:
1. The main topic/theme (one or two words)
2. Any philosophical insight or deep thought (if any)
3. The emotional tone (positive, negative, neutral, mixed)

Memory: {content}

Respond in JSON format:
{{
    "topic": "main topic",
    "insight": "philosophical insight or key wisdom (empty string if none)",
    "tone": "emotional tone",
    "keywords": ["key", "words", "from", "content"]
}}

Only return valid JSON, no additional text.
"""

                try:
                    response = await self._call_external_llm(insight_prompt)
                    analysis = json.loads(response.strip())

                    # Create enriched memory
                    enriched_memory = memory.copy()
                    enriched_memory["analysis"] = analysis
                    enriched_memory["topic"] = analysis.get("topic", "unknown")
                    enriched_memory["insight"] = analysis.get("insight", "")
                    enriched_memory["tone"] = analysis.get("tone", "neutral")
                    enriched_memory["keywords"] = analysis.get("keywords", [])

                    enriched_memories.append(enriched_memory)

                except (json.JSONDecodeError, Exception) as e:
                    logger.warning(f"Failed to analyze memory insight: {e}")
                    # Add memory without enrichment
                    enriched_memory = memory.copy()
                    enriched_memory["topic"] = "unknown"
                    enriched_memory["insight"] = ""
                    enriched_memory["tone"] = "neutral"
                    enriched_memory["keywords"] = []
                    enriched_memories.append(enriched_memory)

            logger.info(f"Enriched {len(enriched_memories)} memories with insights")
            return enriched_memories

        except Exception as e:
            logger.error(f"Error enriching memories: {e}")
            return memories

    def _find_diverse_memory_combinations(self, enriched_memories: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """
        Find diverse memory combinations for cross-topic idea generation.

        Args:
            enriched_memories: List of memories enriched with insights and topics

        Returns:
            List of memory combinations with high diversity
        """
        try:
            if len(enriched_memories) < 3:
                return []

            # Group memories by topic
            topic_groups = {}
            for memory in enriched_memories:
                topic = memory.get("topic", "unknown")
                if topic not in topic_groups:
                    topic_groups[topic] = []
                topic_groups[topic].append(memory)

            # Find combinations of memories from different topics
            combinations = []
            topics = list(topic_groups.keys())

            # Create combinations of 2-3 memories from different topics
            for i, topic1 in enumerate(topics):
                for j, topic2 in enumerate(topics[i+1:], i+1):
                    # Get one memory from each topic
                    mem1 = topic_groups[topic1][0]  # Most recent from topic1
                    mem2 = topic_groups[topic2][0]  # Most recent from topic2

                    # Add a third memory from a different topic if available
                    if len(topics) > 2:
                        for k, topic3 in enumerate(topics):
                            if k != i and k != j and topic_groups[topic3]:
                                mem3 = topic_groups[topic3][0]
                                combinations.append([mem1, mem2, mem3])
                                break
                    else:
                        combinations.append([mem1, mem2])

            # Prioritize combinations with philosophical insights
            combinations_with_insights = []
            combinations_without_insights = []

            for combo in combinations:
                has_insights = any(mem.get("insight", "") for mem in combo)
                if has_insights:
                    combinations_with_insights.append(combo)
                else:
                    combinations_without_insights.append(combo)

            # Return combinations with insights first, then others
            final_combinations = combinations_with_insights + combinations_without_insights

            # Limit to top 3 combinations to avoid overwhelming the LLM
            logger.info(f"Found {len(final_combinations)} diverse memory combinations")
            return final_combinations[:3]

        except Exception as e:
            logger.error(f"Error finding diverse memory combinations: {e}")
            return []

    async def _generate_fusion_ideas(self, memory_combinations: List[List[Dict[str, Any]]], user_id: str) -> List[Dict[str, Any]]:
        """
        Generate ideas through cross-topic fusion of diverse memories.

        Args:
            memory_combinations: List of diverse memory combinations
            user_id: The user ID

        Returns:
            List of generated fusion ideas
        """
        all_ideas = []

        try:
            for i, combo in enumerate(memory_combinations):
                # Prepare memory information for the prompt
                memory_info = []
                for j, memory in enumerate(combo):
                    info = {
                        "content": memory.get("memory", ""),
                        "topic": memory.get("topic", "unknown"),
                        "insight": memory.get("insight", ""),
                        "tone": memory.get("tone", "neutral"),
                        "keywords": memory.get("keywords", [])
                    }
                    memory_info.append(info)

                # Create fusion prompt
                fusion_prompt = f"""
You are a creative AI that generates innovative ideas by fusing different concepts and insights.

Analyze these diverse memories from different topics and generate 1-2 breakthrough ideas by finding unexpected connections:

{self._format_memories_for_fusion(memory_info)}

Generate ideas that:
1. Bridge the gap between these different topics
2. Leverage the philosophical insights if present
3. Create novel solutions or perspectives
4. Are practical yet innovative
5. Represent genuine intellectual breakthroughs

For each idea, provide:
- A compelling title (max 50 characters)
- A detailed description explaining the fusion (max 200 characters)
- The type: "fusion", "synthesis", "breakthrough", or "innovation"
- Confidence level (0.1-1.0)

Respond in JSON format as an array of objects with keys: title, description, type, confidence.
Only return valid JSON, no additional text.
"""

                try:
                    response = await self._call_external_llm(fusion_prompt)
                    ideas_data = json.loads(response.strip())

                    # Convert to our format
                    for idea_data in ideas_data:
                        if isinstance(idea_data, dict):
                            idea = {
                                "id": f"fusion_{user_id}_{datetime.now().timestamp()}_{random.randint(1000, 9999)}",
                                "user_id": user_id,
                                "title": idea_data.get("title", "Untitled Fusion Idea")[:50],
                                "description": idea_data.get("description", "No description")[:200],
                                "type": idea_data.get("type", "fusion"),
                                "confidence": float(idea_data.get("confidence", 0.7)),
                                "created_at": datetime.now().isoformat(),
                                "source": "cross_topic_fusion",
                                "source_topics": [mem.get("topic", "unknown") for mem in combo],
                                "fusion_insights": [mem.get("insight", "") for mem in combo if mem.get("insight")]
                            }
                            all_ideas.append(idea)

                except (json.JSONDecodeError, Exception) as e:
                    logger.warning(f"Failed to generate fusion ideas for combination {i}: {e}")
                    continue

            logger.info(f"Generated {len(all_ideas)} fusion ideas")
            return all_ideas

        except Exception as e:
            logger.error(f"Error generating fusion ideas: {e}")
            return []

    def _format_memories_for_fusion(self, memory_info: List[Dict[str, Any]]) -> str:
        """
        Format memory information for the fusion prompt.

        Args:
            memory_info: List of memory information dictionaries

        Returns:
            Formatted string for the prompt
        """
        formatted = ""
        for i, info in enumerate(memory_info, 1):
            formatted += f"""
Memory {i}:
- Topic: {info['topic']}
- Content: {info['content']}
- Insight: {info['insight'] if info['insight'] else 'None'}
- Tone: {info['tone']}
- Keywords: {', '.join(info['keywords']) if info['keywords'] else 'None'}
"""
        return formatted