#!/usr/bin/env python3
"""
Simple test for Ideas Discovery functionality.
"""

import requests
import json
import time

BASE_URL = "http://localhost:8011"

def test_basic_functionality():
    """Test basic API functionality."""
    print("🔍 Testing basic API functionality...")
    
    # Test service status
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code == 200:
            print("✅ Service is running")
        else:
            print(f"❌ Service returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Service is not accessible: {e}")
        return False
    
    # Test adding a simple memory
    user_id = "simple_test_user"
    
    memory_data = {
        "messages": [{"role": "user", "content": "I love artificial intelligence and machine learning. The potential to transform healthcare is amazing."}],
        "user_id": user_id,
        "metadata": {"source": "test", "topic": "AI"}
    }
    
    try:
        response = requests.post(f"{BASE_URL}/memories", json=memory_data, timeout=30)
        if response.status_code == 200:
            print("✅ Memory added successfully")
            print(f"Response: {response.json()}")
        else:
            print(f"❌ Failed to add memory: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error adding memory: {e}")
        return False
    
    # Wait a bit
    time.sleep(2)
    
    # Test getting memories
    try:
        response = requests.get(f"{BASE_URL}/memories/{user_id}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Retrieved {data.get('count', 0)} memories")
        else:
            print(f"❌ Failed to get memories: {response.status_code}")
    except Exception as e:
        print(f"❌ Error getting memories: {e}")
    
    # Test manual idea discovery
    try:
        response = requests.post(f"{BASE_URL}/ideas/discover/{user_id}", timeout=30)
        if response.status_code == 200:
            print("✅ Idea discovery triggered")
            print(f"Response: {response.json()}")
        else:
            print(f"❌ Failed to trigger idea discovery: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ Error triggering idea discovery: {e}")
    
    # Wait for processing
    time.sleep(5)
    
    # Check for ideas
    try:
        response = requests.get(f"{BASE_URL}/ideas/{user_id}")
        if response.status_code == 200:
            data = response.json()
            ideas = data.get("ideas", [])
            print(f"✅ Found {len(ideas)} ideas")
            
            if ideas:
                for i, idea in enumerate(ideas, 1):
                    print(f"\n💡 Idea {i}:")
                    print(f"   Title: {idea['title']}")
                    print(f"   Description: {idea['description']}")
                    print(f"   Type: {idea['type']}")
                    print(f"   Confidence: {idea['confidence']}")
            else:
                print("ℹ️  No ideas generated yet")
        else:
            print(f"❌ Failed to get ideas: {response.status_code}")
    except Exception as e:
        print(f"❌ Error getting ideas: {e}")
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print(" Simple Ideas Discovery Test ".center(60, "="))
    print("=" * 60)
    
    success = test_basic_functionality()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Basic test completed!")
    else:
        print("❌ Test failed!")
    print("=" * 60)
