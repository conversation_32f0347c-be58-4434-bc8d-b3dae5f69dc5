#!/usr/bin/env python3
"""
Test script for Ideas Discovery functionality.

This script tests the idea discovery feature by:
1. Adding some sample memories
2. Triggering idea discovery
3. Checking for new ideas
"""

import asyncio
import json
import time
from datetime import datetime
import requests

# API base URL
BASE_URL = "http://localhost:8011"

def print_separator(title):
    """Print a separator with a title."""
    print("\n" + "=" * 80)
    print(f" {title} ".center(80, "="))
    print("=" * 80 + "\n")

def print_json(data):
    """Print data as formatted JSON."""
    print(json.dumps(data, indent=2, ensure_ascii=False))

def test_service_status():
    """Test if the service is running."""
    print("🔍 Testing service status...")
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code == 200:
            print("✅ Service is running")
            print_json(response.json())
            return True
        else:
            print(f"❌ Service returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Service is not accessible: {e}")
        return False

def add_sample_memories(user_id):
    """Add sample memories for testing."""
    print(f"📝 Adding sample memories for user: {user_id}")

    # Create diverse memories that should trigger topic switches and idea fusion
    sample_memories = [
        {
            "messages": [{"role": "user", "content": "I'm deeply fascinated by machine learning and AI applications in healthcare. The potential to save lives through intelligent diagnosis is profound."}],
            "user_id": user_id,
            "metadata": {"source": "test", "topic": "AI healthcare"}
        },
        {
            "messages": [{"role": "user", "content": "I've been working on a project to analyze medical images using deep learning. Every algorithm we create could be the difference between early detection and missed opportunities."}],
            "user_id": user_id,
            "metadata": {"source": "test", "topic": "medical imaging"}
        },
        {
            "messages": [{"role": "user", "content": "I'm also passionate about sustainable technology and green energy solutions. We have a moral obligation to future generations to solve the climate crisis."}],
            "user_id": user_id,
            "metadata": {"source": "test", "topic": "green tech"}
        },
        {
            "messages": [{"role": "user", "content": "Recently I've been thinking about how AI could help optimize renewable energy systems. The intersection of intelligence and sustainability could reshape our world."}],
            "user_id": user_id,
            "metadata": {"source": "test", "topic": "AI energy"}
        },
        {
            "messages": [{"role": "user", "content": "I love reading about biomimicry and how nature inspires technological innovations. Nature has already solved many problems we're still struggling with - we just need to listen."}],
            "user_id": user_id,
            "metadata": {"source": "test", "topic": "biomimicry"}
        },
        {
            "messages": [{"role": "user", "content": "Philosophy teaches us that true wisdom comes from understanding the interconnectedness of all things. Every innovation should serve humanity's greater purpose."}],
            "user_id": user_id,
            "metadata": {"source": "test", "topic": "philosophy"}
        },
        {
            "messages": [{"role": "user", "content": "I've been exploring quantum computing lately. The way quantum systems can exist in multiple states simultaneously challenges our fundamental understanding of reality."}],
            "user_id": user_id,
            "metadata": {"source": "test", "topic": "quantum computing"}
        }
    ]

    added_count = 0
    for memory in sample_memories:
        try:
            response = requests.post(f"{BASE_URL}/memories", json=memory, timeout=30)
            if response.status_code == 200:
                print(f"✅ Added memory: {memory['messages'][0]['content'][:50]}...")
                added_count += 1
            else:
                print(f"❌ Failed to add memory: {response.status_code}")
        except Exception as e:
            print(f"❌ Error adding memory: {e}")

    print(f"📊 Successfully added {added_count}/{len(sample_memories)} memories")
    return added_count > 0

def trigger_idea_discovery(user_id):
    """Trigger idea discovery for the user."""
    print(f"💡 Triggering idea discovery for user: {user_id}")

    try:
        response = requests.post(f"{BASE_URL}/ideas/discover/{user_id}", timeout=30)
        if response.status_code == 200:
            print("✅ Idea discovery triggered successfully")
            print_json(response.json())
            return True
        else:
            print(f"❌ Failed to trigger idea discovery: {response.status_code}")
            print(response.text)
            return False
    except Exception as e:
        print(f"❌ Error triggering idea discovery: {e}")
        return False

def check_ideas(user_id):
    """Check for discovered ideas."""
    print(f"🔍 Checking ideas for user: {user_id}")

    try:
        response = requests.get(f"{BASE_URL}/ideas/{user_id}")
        if response.status_code == 200:
            data = response.json()
            ideas = data.get("ideas", [])
            print(f"✅ Found {len(ideas)} ideas")

            if ideas:
                print("\n💡 Discovered Ideas:")
                for i, idea in enumerate(ideas, 1):
                    print(f"\n{i}. {idea['title']}")
                    print(f"   Type: {idea['type']}")
                    print(f"   Description: {idea['description']}")
                    print(f"   Confidence: {idea['confidence']:.2f}")
                    print(f"   Created: {idea['created_at']}")
            else:
                print("ℹ️  No ideas discovered yet")

            return len(ideas)
        else:
            print(f"❌ Failed to get ideas: {response.status_code}")
            return 0
    except Exception as e:
        print(f"❌ Error getting ideas: {e}")
        return 0

def check_new_ideas(user_id, last_check=None):
    """Check for new ideas since last check."""
    print(f"🔔 Checking for new ideas since last check...")

    try:
        params = {"last_check": last_check} if last_check else {}
        response = requests.get(f"{BASE_URL}/ideas/{user_id}/new", params=params)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ New ideas check result:")
            print(f"   Has new ideas: {data['has_new_ideas']}")
            print(f"   New ideas count: {data['new_ideas_count']}")
            return data
        else:
            print(f"❌ Failed to check new ideas: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Error checking new ideas: {e}")
        return None

def main():
    """Main test function."""
    print_separator("Ideas Discovery Test")

    # Test user ID
    user_id = "test_ideas_user"

    # 1. Test service status
    if not test_service_status():
        print("❌ Service is not available. Please start the agent service first.")
        return

    print_separator("Step 1: Adding Sample Memories")

    # 2. Add sample memories
    if not add_sample_memories(user_id):
        print("❌ Failed to add memories. Cannot proceed with idea discovery test.")
        return

    print_separator("Step 2: Triggering Idea Discovery")

    # 3. Trigger idea discovery
    if not trigger_idea_discovery(user_id):
        print("❌ Failed to trigger idea discovery.")
        return

    # 4. Wait a bit for processing
    print("⏳ Waiting for idea discovery to complete...")
    time.sleep(10)

    print_separator("Step 3: Checking Discovered Ideas")

    # 5. Check for ideas
    ideas_count = check_ideas(user_id)

    print_separator("Step 4: Testing New Ideas Check")

    # 6. Test new ideas check
    timestamp_before = datetime.now().isoformat()
    check_new_ideas(user_id)

    # Wait and check again
    time.sleep(2)
    check_new_ideas(user_id, timestamp_before)

    print_separator("Test Summary")

    if ideas_count > 0:
        print("🎉 Ideas discovery test completed successfully!")
        print(f"✅ Discovered {ideas_count} ideas from sample memories")
        print("✅ All API endpoints are working correctly")
    else:
        print("⚠️  Ideas discovery test completed but no ideas were generated")
        print("ℹ️  This might be normal - idea discovery doesn't always generate ideas")
        print("ℹ️  Try running the test again or check the service logs")

if __name__ == "__main__":
    main()
