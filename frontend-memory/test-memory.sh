#!/bin/bash

# Test script for Memory Agent API

USER_ID="test-user-123"
API_BASE="http://localhost:3001/api"

echo "🧠 Testing Memory Agent API..."

# Test 1: Check service status
echo "1. Checking service status..."
curl -s "$API_BASE/" | jq '.'

echo -e "\n2. Adding test memories..."

# Test 2: Add some test memories
curl -s -X POST "$API_BASE/memories" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{"role": "user", "content": "I love programming in Python and JavaScript"}],
    "user_id": "'$USER_ID'",
    "metadata": {"source": "test", "category": "preferences"}
  }' | jq '.'

curl -s -X POST "$API_BASE/memories" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{"role": "user", "content": "My favorite color is blue and I enjoy hiking on weekends"}],
    "user_id": "'$USER_ID'",
    "metadata": {"source": "test", "category": "personal"}
  }' | jq '.'

curl -s -X POST "$API_BASE/memories" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{"role": "user", "content": "I work as a software engineer at a tech company in San Francisco"}],
    "user_id": "'$USER_ID'",
    "metadata": {"source": "test", "category": "work"}
  }' | jq '.'

echo -e "\n3. Retrieving all memories..."

# Test 3: Get all memories
curl -s "$API_BASE/memories/$USER_ID" | jq '.'

echo -e "\n4. Searching memories..."

# Test 4: Search memories
curl -s -X POST "$API_BASE/memories/search" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "programming",
    "user_id": "'$USER_ID'",
    "limit": 5
  }' | jq '.'

echo -e "\n✅ Test completed! Check the frontend at http://localhost:3001"
