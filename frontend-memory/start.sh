#!/bin/bash

# Memory Agent Frontend Startup Script

echo "🧠 Starting Memory Agent Frontend..."

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    pnpm install
fi

# Check if Memory Agent service is running
echo "🔍 Checking Memory Agent service..."
if curl -s http://localhost:8010/ > /dev/null; then
    echo "✅ Memory Agent service is running on port 8010"
else
    echo "❌ Memory Agent service is not running on port 8010"
    echo "   Please make sure the Docker containers are running:"
    echo "   docker ps | grep agent"
    exit 1
fi

# Start the development server
echo "🚀 Starting frontend development server..."
npx vite --port 3001
