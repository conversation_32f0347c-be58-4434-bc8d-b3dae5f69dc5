import axios from 'axios';

const API_BASE = 'http://127.0.0.1:8010';

async function testMemorySystem() {
    console.log('🚀 开始简化内存系统测试');

    const testUserId = `test-user-${Date.now()}`;
    console.log(`📊 测试用户ID: ${testUserId}`);

    try {
        // 1. 测试服务状态
        console.log('\n🔍 测试服务状态...');
        const statusResponse = await axios.get(`${API_BASE}/`);
        console.log('✅ 服务状态正常:', statusResponse.data.status);

        // 2. 添加几条测试内存
        console.log('\n📝 添加测试内存...');
        const memories = [
            '我喜欢喝咖啡',
            '我在北京工作',
            '我喜欢看电影'
        ];

        let addedCount = 0;
        for (let i = 0; i < memories.length; i++) {
            try {
                const startTime = Date.now();
                await axios.post(`${API_BASE}/memories`, {
                    messages: [{ role: 'user', content: memories[i] }],
                    user_id: testUserId,
                    metadata: { source: 'test' }
                });
                const duration = Date.now() - startTime;
                console.log(`✅ 内存 ${i+1} 添加成功 (${duration}ms): ${memories[i]}`);
                addedCount++;
            } catch (error) {
                console.log(`❌ 内存 ${i+1} 添加失败: ${error.message}`);
            }
        }

        // 3. 等待一下让数据处理完成
        console.log('\n⏳ 等待数据处理...');
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 4. 获取所有内存
        console.log('\n📋 获取所有内存...');
        try {
            const startTime = Date.now();
            const response = await axios.get(`${API_BASE}/memories/${testUserId}`);
            const duration = Date.now() - startTime;
            console.log(`✅ 获取内存成功 (${duration}ms): 共 ${response.data.count} 条内存`);
        } catch (error) {
            console.log(`❌ 获取内存失败: ${error.message}`);
        }

        // 5. 测试搜索
        console.log('\n🔍 测试内存搜索...');
        const searchQueries = ['工作', '饮料'];

        let searchSuccessCount = 0;
        for (const query of searchQueries) {
            try {
                const startTime = Date.now();
                const response = await axios.post(`${API_BASE}/memories/search`, {
                    query: query,
                    user_id: testUserId,
                    limit: 5
                }, { timeout: 15000 });
                const duration = Date.now() - startTime;
                console.log(`✅ 搜索 "${query}" 成功 (${duration}ms): 找到 ${response.data.memories.length} 条相关内存`);
                searchSuccessCount++;
            } catch (error) {
                console.log(`❌ 搜索 "${query}" 失败: ${error.message}`);
            }
        }

        // 6. 总结
        console.log('\n================================================================================');
        console.log('📊 测试结果汇总:');
        console.log('================================================================================');
        console.log(`🔧 服务状态: ✅ 正常`);
        console.log(`📝 添加内存: ${addedCount}/${memories.length} 成功`);
        console.log(`🔍 搜索内存: ${searchSuccessCount}/${searchQueries.length} 成功`);

        const totalTests = 1 + memories.length + 1 + searchQueries.length;
        const totalSuccess = 1 + addedCount + 1 + searchSuccessCount;
        const successRate = ((totalSuccess / totalTests) * 100).toFixed(1);

        console.log(`🎯 总体成功率: ${successRate}% (${totalSuccess}/${totalTests})`);

        if (successRate >= 80) {
            console.log('🎉 系统运行良好！');
        } else {
            console.log('⚠️  系统稳定性需要改进');
        }

    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
    }
}

// 运行测试
testMemorySystem().catch(console.error);
