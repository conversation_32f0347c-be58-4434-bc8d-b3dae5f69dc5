<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Memory Agent API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        button { padding: 10px 15px; margin: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
    </style>
</head>
<body>
    <h1>Memory Agent API Test</h1>
    
    <div class="test-section">
        <h3>1. Test Service Status</h3>
        <button onclick="testStatus()">Check Status</button>
        <div id="status-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. Test Add Memory</h3>
        <input type="text" id="user-id" placeholder="User ID" value="test-user">
        <input type="text" id="memory-text" placeholder="Memory content" value="This is a test memory">
        <button onclick="testAddMemory()">Add Memory</button>
        <div id="add-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. Test Get Memories</h3>
        <button onclick="testGetMemories()">Get Memories</button>
        <div id="get-result" class="result"></div>
    </div>

    <script>
        async function testStatus() {
            const resultDiv = document.getElementById('status-result');
            try {
                const response = await fetch('/api/');
                const data = await response.json();
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `<strong>Success:</strong><br><pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>Error:</strong> ${error.message}`;
            }
        }

        async function testAddMemory() {
            const resultDiv = document.getElementById('add-result');
            const userId = document.getElementById('user-id').value;
            const memoryText = document.getElementById('memory-text').value;
            
            try {
                const response = await fetch('/api/memories', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        messages: [{ role: 'user', content: memoryText }],
                        user_id: userId,
                        metadata: { source: 'test_page' }
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `<strong>Success:</strong><br><pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<strong>Error ${response.status}:</strong><br><pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>Network Error:</strong> ${error.message}`;
            }
        }

        async function testGetMemories() {
            const resultDiv = document.getElementById('get-result');
            const userId = document.getElementById('user-id').value;
            
            try {
                const response = await fetch(`/api/memories/${userId}`);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `<strong>Success:</strong><br><pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<strong>Error ${response.status}:</strong><br><pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>Network Error:</strong> ${error.message}`;
            }
        }
    </script>
</body>
</html>
