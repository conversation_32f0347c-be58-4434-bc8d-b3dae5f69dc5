import axios from 'axios';
import { MemoryInput, MemorySearchInput, MemoryResponse, MemorySearchResponse, IdeasResponse, NewIdeasCheckResponse, InspirationsResponse, NewInspirationsCheckResponse } from '@/types/memory';

const api = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000, // 30 second timeout
});

// Add request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url, config.data);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for logging
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.status, response.config.url, response.data);
    return response;
  },
  (error) => {
    console.error('API Response Error:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      url: error.config?.url,
      data: error.response?.data,
      message: error.message
    });
    return Promise.reject(error);
  }
);

export const memoryApi = {
  // Get service status
  getStatus: () => api.get('/'),

  // Get all memories for a user (fast direct database query)
  getAllMemories: (userId: string): Promise<MemorySearchResponse> =>
    api.get(`/memories/${userId}`, { timeout: 10000 }).then(res => res.data),

  // Add a new memory (with extended timeout for LLM processing)
  addMemory: (memoryInput: MemoryInput): Promise<MemoryResponse> =>
    api.post('/memories', memoryInput, { timeout: 120000 }).then(res => res.data),

  // Search memories
  searchMemories: (searchInput: MemorySearchInput): Promise<MemorySearchResponse> =>
    api.post('/memories/search', searchInput).then(res => res.data),

  // Delete a memory
  deleteMemory: (memoryId: string): Promise<MemoryResponse> =>
    api.delete(`/memories/${memoryId}`).then(res => res.data),

  // Delete all memories for a user
  deleteAllUserMemories: (userId: string): Promise<MemoryResponse> =>
    api.delete(`/memories/user/${userId}`).then(res => res.data),

  // Get all ideas for a user
  getUserIdeas: (userId: string): Promise<IdeasResponse> =>
    api.get(`/ideas/${userId}`).then(res => res.data),

  // Trigger idea discovery for a user
  triggerIdeaDiscovery: (userId: string): Promise<MemoryResponse> =>
    api.post(`/ideas/discover/${userId}`).then(res => res.data),

  // Check for new ideas since last check
  checkNewIdeas: (userId: string, lastCheck?: string): Promise<NewIdeasCheckResponse> =>
    api.get(`/ideas/${userId}/new`, {
      params: lastCheck ? { last_check: lastCheck } : {}
    }).then(res => res.data),

  // Get all inspirations for a user
  getUserInspirations: (userId: string): Promise<InspirationsResponse> =>
    api.get(`/inspirations/${userId}`).then(res => res.data),

  // Trigger inspiration mining for a user
  triggerInspirationMining: (userId: string, content?: string): Promise<MemoryResponse> =>
    api.post(`/inspirations/trigger/${userId}`, content ? { content } : {}).then(res => res.data),

  // Check for new inspirations since last check
  checkNewInspirations: (userId: string, lastCheck?: string): Promise<NewInspirationsCheckResponse> =>
    api.get(`/inspirations/${userId}/new`, {
      params: lastCheck ? { last_check: lastCheck } : {}
    }).then(res => res.data),
};
