import React, { useState, useEffect } from 'react';
import { ChatInterface } from '@/components/ChatInterface';
import { MemoryList } from '@/components/MemoryList';
import { InspirationPanel } from '@/components/InspirationPanel';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

import { Brain, List, Settings, X, Sparkles } from 'lucide-react';
import { memoryApi } from '@/lib/api';
import { Toaster } from 'sonner';
import { useInspirationNotification } from '@/hooks/useInspirationNotification';

function App() {
  const [isMemoryPanelOpen, setIsMemoryPanelOpen] = useState(false);
  const [isInspirationPanelOpen, setIsInspirationPanelOpen] = useState(false);
  const [userId, setUserId] = useState('');
  const [isUserIdSet, setIsUserIdSet] = useState(false);
  const [memoryRefreshTrigger, setMemoryRefreshTrigger] = useState(0);
  const [serviceStatus, setServiceStatus] = useState<any>(null);

  // Inspiration notification hook
  const {
    newInspirationsCount,
    resetNotification: resetInspirationNotification
  } = useInspirationNotification({
    userId: isUserIdSet ? userId : '',
    enabled: isUserIdSet,
    pollingInterval: 45000 // Check every 45 seconds when not watching
  });

  useEffect(() => {
    // Load user ID from localStorage
    const savedUserId = localStorage.getItem('memory-agent-user-id');
    if (savedUserId) {
      setUserId(savedUserId);
      setIsUserIdSet(true);
    }

    // Check service status
    checkServiceStatus();

    // Listen for custom events to open inspiration panel
    const handleOpenInspirationPanel = () => {
      setIsInspirationPanelOpen(true);
      resetInspirationNotification(); // Reset notification count when panel is opened
    };

    window.addEventListener('openInspirationPanel', handleOpenInspirationPanel);

    return () => {
      window.removeEventListener('openInspirationPanel', handleOpenInspirationPanel);
    };
  }, [resetInspirationNotification]);

  const checkServiceStatus = async () => {
    try {
      const status = await memoryApi.getStatus();
      setServiceStatus(status.data);
    } catch (error) {
      console.error('Service not available:', error);
    }
  };

  const handleUserIdSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (userId.trim()) {
      localStorage.setItem('memory-agent-user-id', userId.trim());
      setIsUserIdSet(true);
    }
  };

  const handleMemoryAdded = () => {
    setMemoryRefreshTrigger(prev => prev + 1);
    // Optionally open the memory panel to show the new memory
    // setIsMemoryPanelOpen(true);
  };

  const resetUserId = () => {
    localStorage.removeItem('memory-agent-user-id');
    setUserId('');
    setIsUserIdSet(false);
    setIsMemoryPanelOpen(false);
    setIsInspirationPanelOpen(false);
  };

  const handleInspirationPanelToggle = () => {
    setIsInspirationPanelOpen(!isInspirationPanelOpen);
    if (!isInspirationPanelOpen) {
      resetInspirationNotification(); // Reset notification count when panel is opened
    }
  };

  if (!isUserIdSet) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-primary rounded-full flex items-center justify-center mb-4">
              <Brain className="w-6 h-6 text-primary-foreground" />
            </div>
            <CardTitle className="text-2xl">Memory Agent</CardTitle>
            <p className="text-muted-foreground">
              Enter your user ID to start managing your memories
            </p>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleUserIdSubmit} className="space-y-4">
              <Input
                type="text"
                placeholder="Enter your user ID"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
                required
              />
              <Button type="submit" className="w-full">
                Get Started
              </Button>
            </form>

            {serviceStatus && (
              <div className="mt-6 p-3 bg-muted rounded-lg">
                <p className="text-xs text-muted-foreground mb-2">Service Status:</p>
                <div className="text-xs space-y-1">
                  <div className="flex justify-between">
                    <span>Status:</span>
                    <span className={serviceStatus.status === 'ok' ? 'text-green-600' : 'text-red-600'}>
                      {serviceStatus.status}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Memory Service:</span>
                    <span className={serviceStatus.memory_service_available ? 'text-green-600' : 'text-red-600'}>
                      {serviceStatus.memory_service_available ? 'Available' : 'Unavailable'}
                    </span>
                  </div>
                  {serviceStatus.models && (
                    <div className="mt-2 pt-2 border-t border-border">
                      <p className="font-medium">Models:</p>
                      <div className="ml-2">
                        <div>LLM: {serviceStatus.models.llm.model}</div>
                        <div>Embedder: {serviceStatus.models.embedder.model}</div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-card">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
              <Brain className="w-4 h-4 text-primary-foreground" />
            </div>
            <div>
              <h1 className="text-lg font-semibold">Memory Agent</h1>
              <p className="text-xs text-muted-foreground">User: {userId}</p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant={isMemoryPanelOpen ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setIsMemoryPanelOpen(!isMemoryPanelOpen)}
            >
              <List className="w-4 h-4 mr-2" />
              Memories
            </Button>

            <div className="relative">
              <Button
                variant={isInspirationPanelOpen ? 'default' : 'ghost'}
                size="sm"
                onClick={handleInspirationPanelToggle}
              >
                <Sparkles className="w-4 h-4 mr-2" />
                Inspiration
              </Button>
              {newInspirationsCount > 0 && (
                <Badge className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs bg-yellow-500 text-white">
                  {newInspirationsCount > 9 ? '9+' : newInspirationsCount}
                </Badge>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={resetUserId}
            >
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content with Sidebar */}
      <main className="h-[calc(100vh-73px)] flex relative">
        {/* Chat Interface - Main Area */}
        <div className={`flex-1 transition-all duration-300 ${isMemoryPanelOpen ? 'mr-96' : ''}`}>
          <ChatInterface
            userId={userId}
            onMemoryAdded={handleMemoryAdded}
            onOpenMemoryPanel={() => setIsMemoryPanelOpen(true)}
          />
        </div>

        {/* Memory Panel - Right Sidebar */}
        <div className={`fixed right-0 top-[73px] h-[calc(100vh-73px)] w-96 bg-background border-l shadow-lg transform transition-transform duration-300 z-10 ${
          isMemoryPanelOpen ? 'translate-x-0' : 'translate-x-full'
        }`}>
          {/* Panel Header */}
          <div className="flex items-center justify-between p-4 border-b bg-card">
            <div className="flex items-center gap-2">
              <List className="h-5 w-5 text-primary" />
              <h2 className="font-semibold">Memory Bank</h2>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsMemoryPanelOpen(false)}
              className="h-8 w-8"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Memory List */}
          <div className="h-[calc(100%-65px)]">
            <MemoryList userId={userId} refreshTrigger={memoryRefreshTrigger} />
          </div>
        </div>

        {/* Overlay for mobile */}
        {isMemoryPanelOpen && (
          <div
            className="fixed inset-0 bg-black/20 z-5 lg:hidden"
            onClick={() => setIsMemoryPanelOpen(false)}
          />
        )}
      </main>



      {/* Inspiration Panel */}
      <InspirationPanel
        userId={userId}
        isOpen={isInspirationPanelOpen}
        onClose={() => setIsInspirationPanelOpen(false)}
      />

      {/* Toast notifications */}
      <Toaster position="top-right" richColors />
    </div>
  );
}

export default App;
