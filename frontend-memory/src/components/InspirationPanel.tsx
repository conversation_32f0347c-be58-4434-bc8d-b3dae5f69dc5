import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Lightbulb, Sparkles, RefreshCw, X, Brain, Link, Eye, BookOpen } from 'lucide-react';
import { toast } from 'sonner';
import { memoryApi } from '@/lib/api';
import { Inspiration } from '@/types/memory';

interface InspirationPanelProps {
  userId: string;
  isOpen: boolean;
  onClose: () => void;
}

export const InspirationPanel: React.FC<InspirationPanelProps> = ({ userId, isOpen, onClose }) => {
  const [inspirations, setInspirations] = useState<Inspiration[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isMining, setIsMining] = useState(false);

  const loadInspirations = async () => {
    setIsLoading(true);
    try {
      const response = await memoryApi.getUserInspirations(userId);
      setInspirations(response.inspirations || []);
    } catch (error: any) {
      console.error('Error loading inspirations:', error);
      toast.error('Failed to load inspirations', {
        description: 'Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const triggerMining = async () => {
    setIsMining(true);
    try {
      await memoryApi.triggerInspirationMining(userId);
      toast.success('Inspiration mining started!', {
        description: 'We\'ll notify you when new insights are discovered.',
      });
      
      // Refresh inspirations after a short delay
      setTimeout(() => {
        loadInspirations();
      }, 3000);
    } catch (error: any) {
      console.error('Error triggering mining:', error);
      toast.error('Failed to start inspiration mining', {
        description: 'Please try again.',
      });
    } finally {
      setIsMining(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      loadInspirations();
    }
  }, [isOpen, userId]);

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'connection':
        return Link;
      case 'pattern':
        return Brain;
      case 'possibility':
        return Sparkles;
      case 'wisdom':
        return BookOpen;
      default:
        return Eye;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'connection':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'pattern':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'possibility':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'wisdom':
        return 'bg-amber-100 text-amber-800 border-amber-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return 'Unknown time';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed right-0 top-0 h-full w-96 bg-background border-l shadow-lg z-50 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <Lightbulb className="w-5 h-5 text-yellow-500" />
          <h2 className="text-lg font-semibold">Inspirations</h2>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={triggerMining}
            disabled={isMining}
            className="text-xs"
          >
            {isMining ? (
              <RefreshCw className="w-3 h-3 animate-spin mr-1" />
            ) : (
              <Sparkles className="w-3 h-3 mr-1" />
            )}
            {isMining ? 'Mining...' : 'Mine Ideas'}
          </Button>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Inspirations List */}
      <ScrollArea className="flex-1 p-4">
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : inspirations.length === 0 ? (
          <div className="text-center text-muted-foreground py-8">
            <Lightbulb className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p className="text-sm">No inspirations discovered yet.</p>
            <p className="text-xs mt-2">Add more memories and insights will emerge!</p>
          </div>
        ) : (
          <div className="space-y-4">
            {inspirations.map((inspiration) => {
              const TypeIcon = getTypeIcon(inspiration.type);
              return (
                <Card key={inspiration.id} className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-2">
                    <div className="flex items-start justify-between">
                      <CardTitle className="text-sm font-medium leading-tight">
                        {inspiration.title}
                      </CardTitle>
                      <Badge 
                        variant="outline" 
                        className={`text-xs ${getTypeColor(inspiration.type)}`}
                      >
                        <TypeIcon className="w-3 h-3 mr-1" />
                        {inspiration.type}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <p className="text-sm text-muted-foreground mb-3 leading-relaxed">
                      {inspiration.insight}
                    </p>
                    
                    {inspiration.reasoning && (
                      <div className="bg-muted/50 rounded-md p-2 mb-3">
                        <p className="text-xs text-muted-foreground">
                          <strong>Why this emerged:</strong> {inspiration.reasoning}
                        </p>
                      </div>
                    )}

                    {inspiration.trigger_content && (
                      <div className="bg-blue-50 rounded-md p-2 mb-3">
                        <p className="text-xs text-blue-700">
                          <strong>Triggered by:</strong> {inspiration.trigger_content}...
                        </p>
                      </div>
                    )}

                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>{formatDate(inspiration.created_at)}</span>
                      <div className="flex items-center gap-1">
                        <span>Confidence:</span>
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <div
                              key={i}
                              className={`w-2 h-2 rounded-full mr-0.5 ${
                                i < Math.round(inspiration.confidence * 5)
                                  ? 'bg-yellow-400'
                                  : 'bg-gray-200'
                              }`}
                            />
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
      </ScrollArea>

      {/* Footer */}
      <div className="border-t p-4">
        <p className="text-xs text-muted-foreground text-center">
          Inspirations emerge from patterns in your memories and thoughts
        </p>
      </div>
    </div>
  );
};
