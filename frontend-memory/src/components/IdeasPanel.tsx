import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Lightbulb, Sparkles, RefreshCw, X } from 'lucide-react';
import { toast } from 'sonner';
import { memoryApi } from '@/lib/api';
import { Idea } from '@/types/memory';

interface IdeasPanelProps {
  userId: string;
  isOpen: boolean;
  onClose: () => void;
}

export const IdeasPanel: React.FC<IdeasPanelProps> = ({ userId, isOpen, onClose }) => {
  const [ideas, setIdeas] = useState<Idea[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isDiscovering, setIsDiscovering] = useState(false);

  const loadIdeas = async () => {
    setIsLoading(true);
    try {
      const response = await memoryApi.getUserIdeas(userId);
      setIdeas(response.ideas || []);
    } catch (error: any) {
      console.error('Error loading ideas:', error);
      toast.error('Failed to load ideas', {
        description: 'Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const triggerDiscovery = async () => {
    setIsDiscovering(true);
    try {
      await memoryApi.triggerIdeaDiscovery(userId);
      toast.success('Idea discovery started!', {
        description: 'We\'ll notify you when new ideas are discovered.',
      });
      
      // Refresh ideas after a short delay
      setTimeout(() => {
        loadIdeas();
      }, 2000);
    } catch (error: any) {
      console.error('Error triggering discovery:', error);
      toast.error('Failed to start idea discovery', {
        description: 'Please try again.',
      });
    } finally {
      setIsDiscovering(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      loadIdeas();
    }
  }, [isOpen, userId]);

  const getIdeaTypeColor = (type: string) => {
    switch (type) {
      case 'innovation': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'connection': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'opportunity': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'solution': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      case 'inspiration': return 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-y-0 right-0 w-96 bg-background border-l border-border shadow-lg z-50 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center gap-2">
          <Lightbulb className="w-5 h-5 text-yellow-500" />
          <h2 className="text-lg font-semibold">Ideas & Inspirations</h2>
        </div>
        <Button variant="ghost" size="sm" onClick={onClose}>
          <X className="w-4 h-4" />
        </Button>
      </div>

      {/* Controls */}
      <div className="p-4 border-b border-border">
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={loadIdeas}
            disabled={isLoading}
            className="flex-1"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={triggerDiscovery}
            disabled={isDiscovering}
            className="flex-1"
          >
            <Sparkles className={`w-4 h-4 mr-2 ${isDiscovering ? 'animate-pulse' : ''}`} />
            Discover
          </Button>
        </div>
      </div>

      {/* Ideas List */}
      <ScrollArea className="flex-1 p-4">
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : ideas.length === 0 ? (
          <div className="text-center text-muted-foreground py-8">
            <Lightbulb className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p className="text-sm">No ideas discovered yet.</p>
            <p className="text-xs mt-2">Add more memories and try discovering ideas!</p>
          </div>
        ) : (
          <div className="space-y-4">
            {ideas.map((idea) => (
              <Card key={idea.id} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-2">
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-sm font-medium leading-tight">
                      {idea.title}
                    </CardTitle>
                    <Badge className={`text-xs ${getIdeaTypeColor(idea.type)}`}>
                      {idea.type}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <p className="text-sm text-muted-foreground mb-3 leading-relaxed">
                    {idea.description}
                  </p>
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <span>Confidence: {Math.round(idea.confidence * 100)}%</span>
                    <span>{formatDate(idea.created_at)}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </ScrollArea>
    </div>
  );
};
