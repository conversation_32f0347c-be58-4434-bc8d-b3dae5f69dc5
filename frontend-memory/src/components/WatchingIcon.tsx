import React, { useState, useEffect } from 'react';
import { <PERSON>, Brain, Sparkles, Lightbulb } from 'lucide-react';
import { cn } from '@/lib/utils';

interface WatchingIconProps {
  isActive: boolean;
  className?: string;
  size?: number;
  onInspirationFound?: () => void;
}

export const WatchingIcon: React.FC<WatchingIconProps> = ({
  isActive,
  className,
  size = 24,
  onInspirationFound
}) => {
  const [animationState, setAnimationState] = useState<'idle' | 'watching' | 'thinking' | 'inspired'>('idle');
  const [blinkCount, setBlinkCount] = useState(0);

  useEffect(() => {
    if (!isActive) {
      setAnimationState('idle');
      return;
    }

    // Start watching animation
    setAnimationState('watching');
    setBlinkCount(0);

    // Simulate the watching -> thinking -> inspired flow
    const watchingTimer = setTimeout(() => {
      setAnimationState('thinking');
    }, 2000);

    const thinkingTimer = setTimeout(() => {
      // Randomly decide if inspiration is found
      const foundInspiration = Math.random() > 0.3; // 70% chance of finding inspiration
      
      if (foundInspiration) {
        setAnimationState('inspired');
        onInspirationFound?.();
        
        // Return to idle after showing inspiration
        setTimeout(() => {
          setAnimationState('idle');
        }, 3000);
      } else {
        // Return to idle if no inspiration found
        setAnimationState('idle');
      }
    }, 5000);

    return () => {
      clearTimeout(watchingTimer);
      clearTimeout(thinkingTimer);
    };
  }, [isActive, onInspirationFound]);

  // Blinking effect for watching state
  useEffect(() => {
    if (animationState === 'watching') {
      const blinkInterval = setInterval(() => {
        setBlinkCount(prev => prev + 1);
      }, 2000);

      return () => clearInterval(blinkInterval);
    }
  }, [animationState]);

  const getIcon = () => {
    switch (animationState) {
      case 'watching':
        return Eye;
      case 'thinking':
        return Brain;
      case 'inspired':
        return Lightbulb;
      default:
        return Sparkles;
    }
  };

  const getAnimationClasses = () => {
    switch (animationState) {
      case 'watching':
        return 'animate-pulse text-blue-500';
      case 'thinking':
        return 'animate-spin text-purple-500';
      case 'inspired':
        return 'animate-bounce text-yellow-500';
      default:
        return 'text-gray-400';
    }
  };

  const getTooltipText = () => {
    switch (animationState) {
      case 'watching':
        return 'Observing your thoughts...';
      case 'thinking':
        return 'Analyzing patterns and connections...';
      case 'inspired':
        return 'Inspiration discovered!';
      default:
        return 'AI Companion';
    }
  };

  const IconComponent = getIcon();

  return (
    <div 
      className={cn(
        'relative inline-flex items-center justify-center transition-all duration-300',
        className
      )}
      title={getTooltipText()}
    >
      <IconComponent 
        size={size} 
        className={cn(
          'transition-all duration-300',
          getAnimationClasses(),
          animationState === 'watching' && blinkCount % 2 === 1 && 'opacity-30'
        )}
      />
      
      {/* Subtle glow effect for active states */}
      {animationState !== 'idle' && (
        <div 
          className={cn(
            'absolute inset-0 rounded-full blur-sm opacity-30',
            animationState === 'watching' && 'bg-blue-400',
            animationState === 'thinking' && 'bg-purple-400',
            animationState === 'inspired' && 'bg-yellow-400'
          )}
        />
      )}
      
      {/* Ripple effect for inspired state */}
      {animationState === 'inspired' && (
        <div className="absolute inset-0 rounded-full border-2 border-yellow-400 animate-ping opacity-75" />
      )}
    </div>
  );
};

// Hook for managing inspiration notifications
export const useInspirationNotification = (userId: string, enabled: boolean = true) => {
  const [isWatching, setIsWatching] = useState(false);
  const [lastCheck, setLastCheck] = useState<string | null>(null);

  const startWatching = () => {
    if (enabled) {
      setIsWatching(true);
    }
  };

  const stopWatching = () => {
    setIsWatching(false);
  };

  const handleInspirationFound = () => {
    // This will be called when the watching icon detects inspiration
    // The actual inspiration checking will be handled by polling
    console.log('Inspiration animation completed for user:', userId);
  };

  return {
    isWatching,
    startWatching,
    stopWatching,
    handleInspirationFound,
    lastCheck,
    setLastCheck
  };
};
