import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card, CardContent } from '@/components/ui/card';
import { Send, User, Bot } from 'lucide-react';
import { memoryApi } from '@/lib/api';
import { Message } from '@/types/memory';
import { toast } from 'sonner';

interface ChatMessage extends Message {
  id: string;
  timestamp: Date;
}

interface ChatInterfaceProps {
  userId: string;
  onMemoryAdded: () => void;
  onOpenMemoryPanel: () => void;
}

export function ChatInterface({ userId, onMemoryAdded, onOpenMemoryPanel }: ChatInterfaceProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [input, setInput] = useState('');
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const scrollToBottom = () => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight;
      }
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: input.trim(),
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');

    // Show immediate acknowledgment
    const botMessage: ChatMessage = {
      id: (Date.now() + 1).toString(),
      role: 'assistant',
      content: 'I\'ve received your message and I\'m processing it as a memory. You\'ll be notified when it\'s saved.',
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, botMessage]);

    // Show processing toast
    const processingToast = toast.loading('Saving memory...', {
      description: 'Your message is being processed and stored.',
    });

    // Process memory in background
    try {
      console.log('Submitting memory for user:', userId);

      // Add memory to the system (background process)
      const result = await memoryApi.addMemory({
        messages: [{ role: 'user', content: userMessage.content }],
        user_id: userId,
        metadata: {
          timestamp: userMessage.timestamp.toISOString(),
          source: 'chat_interface'
        }
      });

      console.log('Memory added successfully:', result);

      // Show success toast
      toast.success('Memory saved successfully!', {
        id: processingToast,
        description: 'Your message has been stored and can be found in the memory panel.',
        action: {
          label: 'View Memories',
          onClick: () => {
            onOpenMemoryPanel();
          }
        }
      });

      // Update the bot message to reflect success
      setMessages(prev => prev.map(msg =>
        msg.id === botMessage.id
          ? { ...msg, content: 'Perfect! Your memory has been saved successfully. I\'ll remember this for future conversations.' }
          : msg
      ));

      // Trigger memory list refresh
      onMemoryAdded();

    } catch (error: any) {
      console.error('Error adding memory:', error);

      let errorText = 'Failed to save memory. Please try again.';
      let errorDescription = 'There was an error processing your message.';

      if (error.response?.status === 503) {
        errorText = 'Memory service unavailable';
        errorDescription = 'The memory service is currently not responding.';
      } else if (error.response?.status === 500) {
        errorText = 'Server error';
        errorDescription = 'Internal server error occurred while saving.';
      } else if (error.code === 'ECONNABORTED') {
        errorText = 'Request timed out';
        errorDescription = 'The memory service is taking too long to respond.';
      } else if (error.message) {
        errorDescription = error.message;
      }

      // Show error toast
      toast.error(errorText, {
        id: processingToast,
        description: errorDescription,
        action: {
          label: 'Retry',
          onClick: () => {
            // Retry the submission
            handleSubmit(e);
          }
        }
      });

      // Update the bot message to reflect error
      setMessages(prev => prev.map(msg =>
        msg.id === botMessage.id
          ? { ...msg, content: `Sorry, there was an error saving your message: ${errorText}. Please try again.` }
          : msg
      ));
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Chat Messages */}
      <ScrollArea ref={scrollAreaRef} className="flex-1 p-4">
        <div className="space-y-4">
          {messages.length === 0 && (
            <div className="text-center text-muted-foreground py-8">
              <Bot className="mx-auto h-12 w-12 mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">Welcome to Memory Agent</p>
              <p className="text-sm">
                Share your thoughts, experiences, or any information you'd like me to remember.
                I'll store it as a memory for future conversations.
              </p>
            </div>
          )}

          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex gap-3 ${
                message.role === 'user' ? 'justify-end' : 'justify-start'
              }`}
            >
              {message.role === 'assistant' && (
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center">
                    <Bot className="w-4 h-4 text-primary-foreground" />
                  </div>
                </div>
              )}

              <Card className={`max-w-[80%] ${
                message.role === 'user'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-muted'
              }`}>
                <CardContent className="p-3">
                  <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                  <p className={`text-xs mt-2 opacity-70 ${
                    message.role === 'user'
                      ? 'text-primary-foreground/70'
                      : 'text-muted-foreground'
                  }`}>
                    {message.timestamp.toLocaleTimeString()}
                  </p>
                </CardContent>
              </Card>

              {message.role === 'user' && (
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 rounded-full bg-secondary flex items-center justify-center">
                    <User className="w-4 h-4 text-secondary-foreground" />
                  </div>
                </div>
              )}
            </div>
          ))}


        </div>
      </ScrollArea>

      {/* Input Area */}
      <div className="border-t p-4">
        <form onSubmit={handleSubmit} className="flex gap-2">
          <Textarea
            ref={textareaRef}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Share something you'd like me to remember... (Ctrl+Enter to send)"
            className="min-h-[60px] max-h-[120px] resize-none"
          />
          <Button
            type="submit"
            size="icon"
            disabled={!input.trim()}
            className="self-end"
          >
            <Send className="h-4 w-4" />
          </Button>
        </form>
        <p className="text-xs text-muted-foreground mt-2">
          Press Ctrl+Enter to send your message
        </p>
      </div>
    </div>
  );
}
