import { useEffect, useRef, useState } from 'react';
import { toast } from 'sonner';
import { memoryApi } from '@/lib/api';

interface UseIdeasNotificationProps {
  userId: string;
  enabled?: boolean;
  pollingInterval?: number; // in milliseconds
}

export const useIdeasNotification = ({ 
  userId, 
  enabled = true, 
  pollingInterval = 30000 // 30 seconds
}: UseIdeasNotificationProps) => {
  const [lastCheck, setLastCheck] = useState<string | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const [newIdeasCount, setNewIdeasCount] = useState(0);

  const checkForNewIdeas = async () => {
    if (!enabled || !userId) return;

    try {
      const response = await memoryApi.checkNewIdeas(userId, lastCheck || undefined);
      
      if (response.has_new_ideas && response.new_ideas_count > 0) {
        // Show toast notification
        toast.success('💡 New ideas discovered!', {
          description: `${response.new_ideas_count} new ${response.new_ideas_count === 1 ? 'idea' : 'ideas'} found from your memories.`,
          action: {
            label: 'View Ideas',
            onClick: () => {
              // This will be handled by the parent component
              window.dispatchEvent(new CustomEvent('openIdeasPanel'));
            }
          },
          duration: 8000, // Show for 8 seconds
        });

        setNewIdeasCount(response.new_ideas_count);
      }

      // Update last check timestamp
      setLastCheck(new Date().toISOString());
    } catch (error) {
      console.error('Error checking for new ideas:', error);
      // Don't show error toast for polling failures to avoid spam
    }
  };

  const startPolling = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Initial check
    checkForNewIdeas();

    // Set up polling
    intervalRef.current = setInterval(checkForNewIdeas, pollingInterval);
  };

  const stopPolling = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  const resetNotification = () => {
    setNewIdeasCount(0);
    setLastCheck(new Date().toISOString());
  };

  useEffect(() => {
    if (enabled && userId) {
      startPolling();
    } else {
      stopPolling();
    }

    return () => {
      stopPolling();
    };
  }, [enabled, userId, pollingInterval]);

  return {
    newIdeasCount,
    checkForNewIdeas,
    resetNotification,
    startPolling,
    stopPolling
  };
};
