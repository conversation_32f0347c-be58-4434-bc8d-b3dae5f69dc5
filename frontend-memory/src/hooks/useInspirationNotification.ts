import { useEffect, useRef, useState } from 'react';
import { toast } from 'sonner';
import { memoryApi } from '@/lib/api';

interface UseInspirationNotificationProps {
  userId: string;
  enabled?: boolean;
  pollingInterval?: number; // in milliseconds
}

export const useInspirationNotification = ({ 
  userId, 
  enabled = true, 
  pollingInterval = 15000 // 15 seconds - more frequent than ideas
}: UseInspirationNotificationProps) => {
  const [lastCheck, setLastCheck] = useState<string | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const [newInspirationsCount, setNewInspirationsCount] = useState(0);
  const [isWatching, setIsWatching] = useState(false);

  const checkForNewInspirations = async () => {
    if (!enabled || !userId) return;

    try {
      const response = await memoryApi.checkNewInspirations(userId, lastCheck || undefined);
      
      if (response.has_new_inspirations && response.new_inspirations_count > 0) {
        // Show toast notification with inspiration theme
        toast.success('✨ New inspiration discovered!', {
          description: `${response.new_inspirations_count} new ${response.new_inspirations_count === 1 ? 'insight' : 'insights'} emerged from your thoughts.`,
          action: {
            label: 'View Inspiration',
            onClick: () => {
              // This will be handled by the parent component
              window.dispatchEvent(new CustomEvent('openInspirationPanel'));
            }
          },
          duration: 10000, // Show for 10 seconds
          className: 'border-l-4 border-l-yellow-400',
        });

        setNewInspirationsCount(response.new_inspirations_count);
        
        // Stop watching animation after inspiration is found
        setIsWatching(false);
      }

      // Update last check timestamp
      setLastCheck(new Date().toISOString());
    } catch (error) {
      console.error('Error checking for new inspirations:', error);
      // Don't show error toast for polling failures to avoid spam
    }
  };

  const startPolling = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Initial check
    checkForNewInspirations();

    // Set up polling
    intervalRef.current = setInterval(checkForNewInspirations, pollingInterval);
  };

  const stopPolling = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  const resetNotification = () => {
    setNewInspirationsCount(0);
    setLastCheck(new Date().toISOString());
  };

  const startWatching = () => {
    setIsWatching(true);
    // Start more frequent polling when watching
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    intervalRef.current = setInterval(checkForNewInspirations, 5000); // 5 seconds when watching
  };

  const stopWatching = () => {
    setIsWatching(false);
    // Return to normal polling interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    if (enabled) {
      intervalRef.current = setInterval(checkForNewInspirations, pollingInterval);
    }
  };

  useEffect(() => {
    if (enabled && userId) {
      startPolling();
    } else {
      stopPolling();
    }

    return () => {
      stopPolling();
    };
  }, [enabled, userId, pollingInterval]);

  // Listen for memory submission events to start watching
  useEffect(() => {
    const handleMemorySubmission = () => {
      if (enabled && userId) {
        startWatching();
      }
    };

    window.addEventListener('memorySubmitted', handleMemorySubmission);
    
    return () => {
      window.removeEventListener('memorySubmitted', handleMemorySubmission);
    };
  }, [enabled, userId]);

  return {
    newInspirationsCount,
    isWatching,
    checkForNewInspirations,
    resetNotification,
    startPolling,
    stopPolling,
    startWatching,
    stopWatching,
    lastCheck,
    setLastCheck
  };
};
