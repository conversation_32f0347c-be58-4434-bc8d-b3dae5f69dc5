import { useEffect, useRef, useState } from 'react';
import { toast } from 'sonner';
import { memoryApi } from '@/lib/api';

interface UseInspirationNotificationProps {
  userId: string;
  enabled?: boolean;
  pollingInterval?: number; // in milliseconds
}

export const useInspirationNotification = ({
  userId,
  enabled = true,
  pollingInterval = 30000 // 30 seconds - less frequent when not watching
}: UseInspirationNotificationProps) => {
  const [lastCheck, setLastCheck] = useState<string | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const [newInspirationsCount, setNewInspirationsCount] = useState(0);
  const [isWatching, setIsWatching] = useState(false);
  const [watchingStartTime, setWatchingStartTime] = useState<number | null>(null);
  const [notifiedInspirationIds, setNotifiedInspirationIds] = useState<Set<string>>(new Set());

  const checkForNewInspirations = async () => {
    if (!enabled || !userId) return;

    try {
      console.log(`[Inspiration] Checking for new inspirations for user ${userId}${isWatching ? ' (watching)' : ''}`);

      // Get all inspirations to check for truly new ones
      const allInspirations = await memoryApi.getUserInspirations(userId);

      // Filter out inspirations we've already notified about
      const newInspirations = allInspirations.inspirations.filter(
        inspiration => !notifiedInspirationIds.has(inspiration.id)
      );

      if (newInspirations.length > 0) {
        console.log(`[Inspiration] Found ${newInspirations.length} truly new inspirations!`);

        // Show toast notification only once per new inspiration
        toast.success('✨ New inspiration discovered!', {
          description: `${newInspirations.length} new ${newInspirations.length === 1 ? 'insight' : 'insights'} emerged from your thoughts.`,
          action: {
            label: 'View Inspiration',
            onClick: () => {
              // This will be handled by the parent component
              window.dispatchEvent(new CustomEvent('openInspirationPanel'));
            }
          },
          duration: 8000, // Show for 8 seconds
          className: 'border-l-4 border-l-yellow-400',
        });

        // Mark these inspirations as notified
        const newNotifiedIds = new Set(notifiedInspirationIds);
        newInspirations.forEach(inspiration => {
          newNotifiedIds.add(inspiration.id);
        });
        setNotifiedInspirationIds(newNotifiedIds);

        setNewInspirationsCount(newInspirations.length);

        // Stop watching animation after inspiration is found
        setIsWatching(false);
        setWatchingStartTime(null);
      } else {
        console.log(`[Inspiration] No new inspirations found for user ${userId}`);
      }

      // Update last check timestamp
      setLastCheck(new Date().toISOString());
    } catch (error) {
      console.error('Error checking for new inspirations:', error);
      // Don't show error toast for polling failures to avoid spam
    }
  };

  const startPolling = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Initial check
    checkForNewInspirations();

    // Set up polling
    intervalRef.current = setInterval(checkForNewInspirations, pollingInterval);
  };

  const stopPolling = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  const resetNotification = () => {
    setNewInspirationsCount(0);
    setLastCheck(new Date().toISOString());
    // Don't clear notifiedInspirationIds to prevent re-notification of same inspirations
  };

  const startWatching = () => {
    setIsWatching(true);
    setWatchingStartTime(Date.now());

    // Start more frequent polling when watching, but with intelligent timing
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Check immediately when starting to watch
    checkForNewInspirations();

    // Then check every 10 seconds for the first 2 minutes
    intervalRef.current = setInterval(checkForNewInspirations, 10000);

    // After 2 minutes, automatically stop watching and return to normal polling
    setTimeout(() => {
      if (isWatching) {
        stopWatching();
      }
    }, 120000); // 2 minutes
  };

  const stopWatching = () => {
    setIsWatching(false);
    setWatchingStartTime(null);

    // Return to normal polling interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    if (enabled) {
      intervalRef.current = setInterval(checkForNewInspirations, pollingInterval);
    }
  };

  useEffect(() => {
    if (enabled && userId) {
      startPolling();
    } else {
      stopPolling();
    }

    return () => {
      stopPolling();
    };
  }, [enabled, userId, pollingInterval]);

  // Listen for memory submission events to start watching
  useEffect(() => {
    const handleMemorySubmission = () => {
      if (enabled && userId) {
        startWatching();
      }
    };

    window.addEventListener('memorySubmitted', handleMemorySubmission);

    return () => {
      window.removeEventListener('memorySubmitted', handleMemorySubmission);
    };
  }, [enabled, userId]);

  return {
    newInspirationsCount,
    isWatching,
    checkForNewInspirations,
    resetNotification,
    startPolling,
    stopPolling,
    startWatching,
    stopWatching,
    lastCheck,
    setLastCheck
  };
};
