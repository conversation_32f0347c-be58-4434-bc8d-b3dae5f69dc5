#!/usr/bin/env node

import axios from 'axios';

// 配置
const BASE_URL = 'http://localhost:3001/api';
const USER_ID = 'test-user-' + Date.now();
const TEST_COUNT = 10;

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 120000, // 2分钟超时
});

// 测试数据
const testMemories = [
  {
    messages: [{ role: 'user', content: '我喜欢喝咖啡，特别是拿铁' }],
    user_id: USER_ID,
    metadata: { source: 'test', category: 'preference' }
  },
  {
    messages: [{ role: 'user', content: '我的生日是3月15日' }],
    user_id: USER_ID,
    metadata: { source: 'test', category: 'personal' }
  },
  {
    messages: [{ role: 'user', content: '我在北京工作，是一名软件工程师' }],
    user_id: USER_ID,
    metadata: { source: 'test', category: 'work' }
  },
  {
    messages: [{ role: 'user', content: '我喜欢看科幻电影，最喜欢的是《星际穿越》' }],
    user_id: USER_ID,
    metadata: { source: 'test', category: 'entertainment' }
  },
  {
    messages: [{ role: 'user', content: '我有一只猫，名字叫小白' }],
    user_id: USER_ID,
    metadata: { source: 'test', category: 'pet' }
  },
  {
    messages: [{ role: 'user', content: '我每天早上7点起床，晚上11点睡觉' }],
    user_id: USER_ID,
    metadata: { source: 'test', category: 'routine' }
  },
  {
    messages: [{ role: 'user', content: '我喜欢跑步，每周跑3次，每次5公里' }],
    user_id: USER_ID,
    metadata: { source: 'test', category: 'exercise' }
  },
  {
    messages: [{ role: 'user', content: '我最喜欢的颜色是蓝色' }],
    user_id: USER_ID,
    metadata: { source: 'test', category: 'preference' }
  },
  {
    messages: [{ role: 'user', content: '我会说中文、英文和日文' }],
    user_id: USER_ID,
    metadata: { source: 'test', category: 'skill' }
  },
  {
    messages: [{ role: 'user', content: '我最喜欢的食物是火锅' }],
    user_id: USER_ID,
    metadata: { source: 'test', category: 'food' }
  }
];

// 搜索查询
const searchQueries = [
  '咖啡',
  '生日',
  '工作',
  '电影',
  '宠物',
  '运动',
  '颜色',
  '语言',
  '食物'
];

// 工具函数
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function formatTime() {
  return new Date().toISOString();
}

// 测试服务状态
async function testServiceStatus() {
  console.log(`[${formatTime()}] 🔍 测试服务状态...`);
  try {
    const response = await api.get('/');
    console.log(`[${formatTime()}] ✅ 服务状态正常:`, response.data.status);
    return true;
  } catch (error) {
    console.log(`[${formatTime()}] ❌ 服务状态检查失败:`, error.message);
    return false;
  }
}

// 添加内存
async function addMemory(memoryData, index) {
  console.log(`[${formatTime()}] 📝 添加内存 ${index + 1}/${testMemories.length}: ${memoryData.messages[0].content.substring(0, 30)}...`);
  try {
    const startTime = Date.now();
    const response = await api.post('/memories', memoryData);
    const duration = Date.now() - startTime;
    console.log(`[${formatTime()}] ✅ 内存添加成功 (${duration}ms):`, response.data.message || 'OK');
    return { success: true, duration, response: response.data };
  } catch (error) {
    console.log(`[${formatTime()}] ❌ 内存添加失败:`, error.message);
    if (error.response) {
      console.log(`[${formatTime()}] 错误详情:`, error.response.data);
    }
    return { success: false, error: error.message };
  }
}

// 获取所有内存
async function getAllMemories() {
  console.log(`[${formatTime()}] 📋 获取所有内存...`);
  try {
    const startTime = Date.now();
    const response = await api.get(`/memories/${USER_ID}`);
    const duration = Date.now() - startTime;
    const count = response.data.memories ? response.data.memories.length : 0;
    console.log(`[${formatTime()}] ✅ 获取内存成功 (${duration}ms): 共 ${count} 条内存`);
    return { success: true, duration, count, memories: response.data.memories };
  } catch (error) {
    console.log(`[${formatTime()}] ❌ 获取内存失败:`, error.message);
    return { success: false, error: error.message };
  }
}

// 搜索内存
async function searchMemories(query, index) {
  console.log(`[${formatTime()}] 🔍 搜索内存 ${index + 1}/${searchQueries.length}: "${query}"`);
  try {
    const startTime = Date.now();
    const response = await api.post('/memories/search', {
      query,
      user_id: USER_ID,
      limit: 5
    });
    const duration = Date.now() - startTime;
    const count = response.data.memories ? response.data.memories.length : 0;
    console.log(`[${formatTime()}] ✅ 搜索成功 (${duration}ms): 找到 ${count} 条相关内存`);
    return { success: true, duration, count, memories: response.data.memories };
  } catch (error) {
    console.log(`[${formatTime()}] ❌ 搜索失败:`, error.message);
    return { success: false, error: error.message };
  }
}

// 主测试函数
async function runStabilityTest() {
  console.log(`[${formatTime()}] 🚀 开始内存系统稳定性测试`);
  console.log(`[${formatTime()}] 📊 测试用户ID: ${USER_ID}`);
  console.log(`[${formatTime()}] 📊 计划添加 ${testMemories.length} 条内存`);
  console.log(`[${formatTime()}] 📊 计划执行 ${searchQueries.length} 次搜索`);
  console.log('='.repeat(80));

  const results = {
    serviceStatus: false,
    addMemory: { success: 0, failed: 0, totalDuration: 0 },
    getAllMemories: { success: 0, failed: 0, totalDuration: 0 },
    searchMemories: { success: 0, failed: 0, totalDuration: 0 }
  };

  // 1. 测试服务状态
  results.serviceStatus = await testServiceStatus();
  if (!results.serviceStatus) {
    console.log(`[${formatTime()}] ❌ 服务不可用，测试终止`);
    return results;
  }

  await sleep(1000);

  // 2. 添加内存测试
  console.log(`\n[${formatTime()}] 📝 开始添加内存测试...`);
  for (let i = 0; i < testMemories.length; i++) {
    const result = await addMemory(testMemories[i], i);
    if (result.success) {
      results.addMemory.success++;
      results.addMemory.totalDuration += result.duration;
    } else {
      results.addMemory.failed++;
    }

    // 添加间隔，避免过快请求
    await sleep(2000);
  }

  // 3. 获取所有内存测试
  console.log(`\n[${formatTime()}] 📋 开始获取内存测试...`);
  for (let i = 0; i < 3; i++) {
    const result = await getAllMemories();
    if (result.success) {
      results.getAllMemories.success++;
      results.getAllMemories.totalDuration += result.duration;
    } else {
      results.getAllMemories.failed++;
    }
    await sleep(1000);
  }

  // 4. 搜索内存测试
  console.log(`\n[${formatTime()}] 🔍 开始搜索内存测试...`);
  for (let i = 0; i < searchQueries.length; i++) {
    const result = await searchMemories(searchQueries[i], i);
    if (result.success) {
      results.searchMemories.success++;
      results.searchMemories.totalDuration += result.duration;
    } else {
      results.searchMemories.failed++;
    }
    await sleep(1000);
  }

  // 5. 输出测试结果
  console.log('\n' + '='.repeat(80));
  console.log(`[${formatTime()}] 📊 测试结果汇总:`);
  console.log('='.repeat(80));

  console.log(`🔧 服务状态: ${results.serviceStatus ? '✅ 正常' : '❌ 异常'}`);

  console.log(`📝 添加内存: ${results.addMemory.success}/${results.addMemory.success + results.addMemory.failed} 成功`);
  if (results.addMemory.success > 0) {
    console.log(`   平均耗时: ${Math.round(results.addMemory.totalDuration / results.addMemory.success)}ms`);
  }

  console.log(`📋 获取内存: ${results.getAllMemories.success}/${results.getAllMemories.success + results.getAllMemories.failed} 成功`);
  if (results.getAllMemories.success > 0) {
    console.log(`   平均耗时: ${Math.round(results.getAllMemories.totalDuration / results.getAllMemories.success)}ms`);
  }

  console.log(`🔍 搜索内存: ${results.searchMemories.success}/${results.searchMemories.success + results.searchMemories.failed} 成功`);
  if (results.searchMemories.success > 0) {
    console.log(`   平均耗时: ${Math.round(results.searchMemories.totalDuration / results.searchMemories.success)}ms`);
  }

  const totalOperations = results.addMemory.success + results.getAllMemories.success + results.searchMemories.success;
  const totalFailed = results.addMemory.failed + results.getAllMemories.failed + results.searchMemories.failed;
  const successRate = totalOperations / (totalOperations + totalFailed) * 100;

  console.log(`\n🎯 总体成功率: ${successRate.toFixed(1)}% (${totalOperations}/${totalOperations + totalFailed})`);

  if (successRate >= 90) {
    console.log(`[${formatTime()}] 🎉 系统稳定性测试通过！`);
  } else {
    console.log(`[${formatTime()}] ⚠️  系统稳定性需要改进`);
  }

  return results;
}

// 运行测试
runStabilityTest().catch(error => {
  console.error(`[${formatTime()}] 💥 测试过程中发生错误:`, error);
  process.exit(1);
});

export { runStabilityTest };
