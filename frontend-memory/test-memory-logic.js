import axios from 'axios';

const API_BASE = 'http://127.0.0.1:8010';

async function testMemoryLogic() {
    console.log('🔍 测试内存添加逻辑');
    
    const testUserId = `test-logic-${Date.now()}`;
    console.log(`📊 测试用户ID: ${testUserId}`);
    
    try {
        // 1. 清理用户数据
        console.log('\n🧹 清理测试用户数据...');
        try {
            await axios.delete(`${API_BASE}/memories/user/${testUserId}`);
            console.log('✅ 清理完成');
        } catch (error) {
            console.log('ℹ️  用户数据为空，无需清理');
        }
        
        // 2. 添加第一条内存
        console.log('\n📝 添加第一条内存...');
        const memory1 = "我今天学习了Python编程";
        const result1 = await axios.post(`${API_BASE}/memories`, {
            messages: [{ role: 'user', content: memory1 }],
            user_id: testUserId,
            metadata: { source: 'test1', timestamp: new Date().toISOString() }
        }, { timeout: 60000 });
        
        console.log(`✅ 第一条内存添加成功: ${memory1}`);
        console.log(`   返回ID: ${result1.data.id}`);
        
        // 3. 等待处理完成
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 4. 检查数据库中的内容
        console.log('\n📋 检查数据库中的内容...');
        const memories1 = await axios.get(`${API_BASE}/memories/${testUserId}`);
        console.log(`✅ 数据库中有 ${memories1.data.count} 条内存:`);
        memories1.data.memories.forEach((mem, index) => {
            console.log(`   ${index + 1}. "${mem.memory}" (ID: ${mem.id})`);
        });
        
        // 5. 添加第二条不同的内存
        console.log('\n📝 添加第二条内存...');
        const memory2 = "我喜欢喝咖啡";
        const result2 = await axios.post(`${API_BASE}/memories`, {
            messages: [{ role: 'user', content: memory2 }],
            user_id: testUserId,
            metadata: { source: 'test2', timestamp: new Date().toISOString() }
        }, { timeout: 60000 });
        
        console.log(`✅ 第二条内存添加成功: ${memory2}`);
        console.log(`   返回ID: ${result2.data.id}`);
        
        // 6. 等待处理完成
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 7. 再次检查数据库中的内容
        console.log('\n📋 再次检查数据库中的内容...');
        const memories2 = await axios.get(`${API_BASE}/memories/${testUserId}`);
        console.log(`✅ 数据库中有 ${memories2.data.count} 条内存:`);
        memories2.data.memories.forEach((mem, index) => {
            console.log(`   ${index + 1}. "${mem.memory}" (ID: ${mem.id})`);
        });
        
        // 8. 验证结果
        console.log('\n🔍 验证结果...');
        if (memories2.data.count === 2) {
            console.log('✅ 成功：两条内存都被正确保存');
            
            // 检查内容是否正确
            const savedMemories = memories2.data.memories.map(m => m.memory);
            const hasMemory1 = savedMemories.some(m => m.includes('Python') || m.includes('编程'));
            const hasMemory2 = savedMemories.some(m => m.includes('咖啡') || m.includes('coffee'));
            
            if (hasMemory1 && hasMemory2) {
                console.log('✅ 内容验证通过：两条内存内容都正确保存');
            } else {
                console.log('❌ 内容验证失败：内存内容不正确');
                console.log('   期望包含: Python/编程 和 咖啡/coffee');
                console.log('   实际内容:', savedMemories);
            }
        } else {
            console.log(`❌ 失败：期望2条内存，实际${memories2.data.count}条`);
        }
        
        // 9. 清理测试数据
        console.log('\n🧹 清理测试数据...');
        await axios.delete(`${API_BASE}/memories/user/${testUserId}`);
        console.log('✅ 测试数据清理完成');
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
        if (error.response) {
            console.error('   响应状态:', error.response.status);
            console.error('   响应数据:', error.response.data);
        }
    }
}

// 运行测试
testMemoryLogic().catch(console.error);
