#!/bin/bash

echo "🔍 Diagnosing Memory Agent Issues..."

echo "1. Checking if services are running..."
echo "Frontend (port 3001):"
curl -s -m 5 http://localhost:3001/ > /dev/null && echo "✅ Frontend is running" || echo "❌ Frontend is not responding"

echo "Backend direct (port 8010):"
curl -s -m 5 http://localhost:8010/ > /dev/null && echo "✅ Backend is running" || echo "❌ Backend is not responding"

echo "Frontend proxy to backend (port 3001/api):"
curl -s -m 5 http://localhost:3001/api/ > /dev/null && echo "✅ Proxy is working" || echo "❌ Proxy is not working"

echo -e "\n2. Testing backend status..."
echo "Backend status response:"
curl -s -m 10 http://localhost:8010/ | jq '.' 2>/dev/null || echo "Failed to get status"

echo -e "\n3. Testing simple memory addition (with timeout)..."
timeout 15s curl -X POST http://localhost:8010/memories \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{"role": "user", "content": "Simple test"}],
    "user_id": "diagnose-test",
    "metadata": {"source": "diagnose"}
  }' 2>/dev/null || echo "Memory addition timed out or failed"

echo -e "\n4. Checking Docker container logs (last 5 lines)..."
docker logs agent-agent-memory-1 --tail 5 2>/dev/null || echo "Could not get Docker logs"

echo -e "\n5. Checking if Ollama is responding..."
curl -s -m 5 http://localhost:11434/api/tags > /dev/null && echo "✅ Ollama is responding" || echo "❌ Ollama is not responding"

echo -e "\nDiagnosis complete!"
